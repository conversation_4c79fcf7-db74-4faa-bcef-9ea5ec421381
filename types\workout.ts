export interface Exercise {
  id: string;
  name: string;
  category: string;
  muscleGroups: string[];
  equipment: string[];
  instructions: string[];
  videoUrl?: string;
  imageUrl?: string;
  isCustom: boolean;
  createdBy?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface WorkoutSet {
  id: string;
  exerciseId: string;
  setNumber: number;
  reps?: number;
  weight?: number;
  duration?: number; // in seconds
  distance?: number; // in meters
  rpe?: number; // Rate of Perceived Exertion (1-10)
  setType: 'warmup' | 'normal' | 'dropset' | 'failure' | 'superset';
  restTime?: number; // in seconds
  notes?: string;
  completed: boolean;
}

export interface WorkoutExercise {
  id: string;
  exerciseId: string;
  sets: WorkoutSet[];
  notes?: string;
  supersetGroup?: string; // Groups exercises in supersets
}

export interface WorkoutLog {
  id: string;
  userId: string;
  title: string;
  date: Date;
  startTime: Date;
  endTime?: Date;
  exercises: WorkoutExercise[];
  notes?: string;
  difficulty: number; // 1-10 rating
  totalVolume: number; // calculated weight x reps
  programId?: string;
  templateId?: string; // Reference to workout template
  isPublic: boolean;
  likes: number;
  comments: Comment[];
  tags: string[];
  location?: string;
  photos?: string[];
  videos?: string[]; // Form check videos
  progressPhotos?: ProgressPhoto[];
  bodyMeasurements?: BodyMeasurement[];
  personalRecords?: PersonalRecord[];
  isTemplate: boolean; // Can be saved as template
  templateName?: string;
  sharedFrom?: string; // User ID who shared this workout
  copyCount?: number; // How many times this was copied
}

export interface WorkoutTemplate {
  id: string;
  name: string;
  description?: string;
  createdBy: string;
  exercises: TemplateExercise[];
  estimatedDuration: number; // in minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  tags: string[];
  isPublic: boolean;
  uses: number; // How many times it's been used
  rating: number;
  reviews: TemplateReview[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateExercise {
  exerciseId: string;
  order: number;
  targetSets: number;
  targetReps?: number;
  targetWeight?: number;
  restTime?: number; // seconds
  notes?: string;
  supersetGroup?: string;
}

export interface TemplateReview {
  id: string;
  userId: string;
  rating: number;
  comment?: string;
  date: Date;
}

export interface ProgressPhoto {
  id: string;
  url: string;
  type: 'front' | 'side' | 'back' | 'pose' | 'comparison';
  date: Date;
  notes?: string;
  bodyWeight?: number;
  bodyFatPercentage?: number;
}

export interface BodyMeasurement {
  id: string;
  date: Date;
  weight?: number;
  bodyFatPercentage?: number;
  muscleMass?: number;
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    biceps?: number;
    thighs?: number;
    neck?: number;
    shoulders?: number;
    forearms?: number;
    calves?: number;
  };
  notes?: string;
}

export interface PersonalRecord {
  id: string;
  exerciseId: string;
  exerciseName: string;
  type: '1RM' | 'volume' | 'reps' | 'time' | 'distance';
  value: number;
  unit: 'kg' | 'lbs' | 'reps' | 'seconds' | 'minutes' | 'km' | 'miles';
  date: Date;
  workoutId?: string;
  previousRecord?: number;
  improvement?: number;
  verified: boolean;
}

export interface Program {
  id: string;
  title: string;
  description: string;
  createdBy: string;
  duration: number; // in weeks
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  weeks: ProgramWeek[];
  price?: number;
  isPremium: boolean;
  subscribers: number;
  rating: number;
  reviews: Review[];
  tags: string[];
  imageUrl?: string;
  isPublic: boolean;
}

export interface ProgramWeek {
  weekNumber: number;
  workouts: ProgramWorkout[];
}

export interface ProgramWorkout {
  id: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  title: string;
  exercises: WorkoutExercise[];
  restDayBefore?: boolean;
  notes?: string;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  clubId: string;
  createdBy: string;
  date: Date;
  startTime: Date;
  endTime: Date;
  location?: string;
  maxAttendees?: number;
  attendees: string[]; // user IDs
  price?: number;
  isVirtual: boolean;
  streamUrl?: string;
  category: string;
  imageUrl?: string;
}

export interface PersonalRecord {
  id: string;
  userId: string;
  exerciseId: string;
  recordType: 'max_weight' | 'max_reps' | 'max_volume' | 'best_time';
  value: number;
  date: Date;
  workoutId: string;
}

export interface BodyMeasurement {
  id: string;
  userId: string;
  date: Date;
  weight?: number;
  bodyFat?: number;
  muscleMass?: number;
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    biceps?: number;
    thighs?: number;
    neck?: number;
  };
  photos?: string[];
}

export interface WorkoutAnalytics {
  totalWorkouts: number;
  totalHours: number;
  totalVolume: number;
  averageWorkoutTime: number;
  workoutStreak: number;
  muscleGroupDistribution: { [muscle: string]: number };
  strengthProgress: PersonalRecord[];
  weeklyStats: {
    week: Date;
    workouts: number;
    volume: number;
    duration: number;
  }[];
}

export interface Comment {
  id: string;
  userId: string;
  workoutId: string;
  content: string;
  timestamp: Date;
  likes: number;
  replies?: Comment[];
}

export interface Review {
  id: string;
  userId: string;
  programId: string;
  rating: number;
  content: string;
  timestamp: Date;
  verified: boolean;
}