import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LineChart } from 'react-native-svg-charts';
import GlassContainer from '../ui/GlassContainer';
import { Colors } from '../../constants/Colors';
import { BodyMeasurement } from '../../types/workout';

interface BodyMeasurementsProps {
  visible: boolean;
  onClose: () => void;
  measurements: BodyMeasurement[];
  onAddMeasurement: (measurement: Omit<BodyMeasurement, 'id'>) => void;
  onDeleteMeasurement: (measurementId: string) => void;
}

const { width } = Dimensions.get('window');

// Mock measurement data
const mockMeasurements: BodyMeasurement[] = [
  {
    id: '1',
    date: new Date('2024-01-01'),
    weight: 75,
    bodyFatPercentage: 18,
    muscleMass: 35,
    measurements: {
      chest: 100,
      waist: 80,
      hips: 95,
      biceps: 35,
      thighs: 55,
      neck: 38,
    },
    notes: 'Starting measurements',
  },
  {
    id: '2',
    date: new Date('2024-02-01'),
    weight: 73,
    bodyFatPercentage: 16,
    muscleMass: 36,
    measurements: {
      chest: 102,
      waist: 78,
      hips: 94,
      biceps: 36,
      thighs: 56,
      neck: 38,
    },
    notes: 'Good progress this month',
  },
  {
    id: '3',
    date: new Date('2024-03-01'),
    weight: 74,
    bodyFatPercentage: 15,
    muscleMass: 37,
    measurements: {
      chest: 104,
      waist: 77,
      hips: 94,
      biceps: 37,
      thighs: 57,
      neck: 39,
    },
  },
];

const measurementFields = [
  { key: 'chest', label: 'Chest', icon: 'body-outline' },
  { key: 'waist', label: 'Waist', icon: 'body-outline' },
  { key: 'hips', label: 'Hips', icon: 'body-outline' },
  { key: 'biceps', label: 'Biceps', icon: 'fitness-outline' },
  { key: 'thighs', label: 'Thighs', icon: 'body-outline' },
  { key: 'neck', label: 'Neck', icon: 'body-outline' },
  { key: 'shoulders', label: 'Shoulders', icon: 'body-outline' },
  { key: 'forearms', label: 'Forearms', icon: 'fitness-outline' },
  { key: 'calves', label: 'Calves', icon: 'body-outline' },
];

export default function BodyMeasurements({ 
  visible, 
  onClose, 
  measurements = mockMeasurements, 
  onAddMeasurement, 
  onDeleteMeasurement 
}: BodyMeasurementsProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<'weight' | 'bodyFat' | 'muscle' | string>('weight');
  const [newMeasurement, setNewMeasurement] = useState({
    weight: '',
    bodyFatPercentage: '',
    muscleMass: '',
    measurements: {} as Record<string, string>,
    notes: '',
  });

  const getChartData = (metric: string) => {
    return measurements.map(m => {
      if (metric === 'weight') return m.weight || 0;
      if (metric === 'bodyFat') return m.bodyFatPercentage || 0;
      if (metric === 'muscle') return m.muscleMass || 0;
      return m.measurements[metric as keyof typeof m.measurements] || 0;
    }).filter(value => value > 0);
  };

  const getLatestValue = (metric: string) => {
    const latest = measurements[measurements.length - 1];
    if (!latest) return 0;
    
    if (metric === 'weight') return latest.weight || 0;
    if (metric === 'bodyFat') return latest.bodyFatPercentage || 0;
    if (metric === 'muscle') return latest.muscleMass || 0;
    return latest.measurements[metric as keyof typeof latest.measurements] || 0;
  };

  const getChange = (metric: string) => {
    if (measurements.length < 2) return 0;
    
    const latest = measurements[measurements.length - 1];
    const previous = measurements[measurements.length - 2];
    
    let latestValue = 0;
    let previousValue = 0;
    
    if (metric === 'weight') {
      latestValue = latest.weight || 0;
      previousValue = previous.weight || 0;
    } else if (metric === 'bodyFat') {
      latestValue = latest.bodyFatPercentage || 0;
      previousValue = previous.bodyFatPercentage || 0;
    } else if (metric === 'muscle') {
      latestValue = latest.muscleMass || 0;
      previousValue = previous.muscleMass || 0;
    } else {
      latestValue = latest.measurements[metric as keyof typeof latest.measurements] || 0;
      previousValue = previous.measurements[metric as keyof typeof previous.measurements] || 0;
    }
    
    return latestValue - previousValue;
  };

  const handleAddMeasurement = () => {
    const measurement: Omit<BodyMeasurement, 'id'> = {
      date: new Date(),
      weight: newMeasurement.weight ? parseFloat(newMeasurement.weight) : undefined,
      bodyFatPercentage: newMeasurement.bodyFatPercentage ? parseFloat(newMeasurement.bodyFatPercentage) : undefined,
      muscleMass: newMeasurement.muscleMass ? parseFloat(newMeasurement.muscleMass) : undefined,
      measurements: Object.keys(newMeasurement.measurements).reduce((acc, key) => {
        const value = newMeasurement.measurements[key];
        if (value) {
          acc[key as keyof typeof acc] = parseFloat(value);
        }
        return acc;
      }, {} as any),
      notes: newMeasurement.notes || undefined,
    };

    onAddMeasurement(measurement);
    setShowAddForm(false);
    resetForm();
  };

  const resetForm = () => {
    setNewMeasurement({
      weight: '',
      bodyFatPercentage: '',
      muscleMass: '',
      measurements: {},
      notes: '',
    });
  };

  const updateMeasurementField = (field: string, value: string) => {
    setNewMeasurement(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [field]: value,
      },
    }));
  };

  const renderMetricCard = (metric: string, label: string, unit: string, icon: string) => {
    const value = getLatestValue(metric);
    const change = getChange(metric);
    const chartData = getChartData(metric);
    
    return (
      <TouchableOpacity
        key={metric}
        style={[styles.metricCard, selectedMetric === metric && styles.selectedMetricCard]}
        onPress={() => setSelectedMetric(metric)}
      >
        <GlassContainer style={styles.metricContainer} intensity="light">
          <View style={styles.metricHeader}>
            <Ionicons name={icon as any} size={20} color={Colors.accent.primary} />
            <Text style={styles.metricLabel}>{label}</Text>
          </View>
          <Text style={styles.metricValue}>
            {value > 0 ? `${value}${unit}` : '--'}
          </Text>
          {change !== 0 && (
            <View style={styles.metricChange}>
              <Ionicons 
                name={change > 0 ? 'trending-up' : 'trending-down'} 
                size={12} 
                color={change > 0 ? '#4CAF50' : '#F44336'} 
              />
              <Text style={[
                styles.changeText,
                { color: change > 0 ? '#4CAF50' : '#F44336' }
              ]}>
                {Math.abs(change).toFixed(1)}{unit}
              </Text>
            </View>
          )}
          {chartData.length > 1 && (
            <View style={styles.miniChart}>
              <LineChart
                style={{ height: 30, width: '100%' }}
                data={chartData}
                svg={{ stroke: Colors.accent.primary, strokeWidth: 2 }}
                contentInset={{ top: 5, bottom: 5 }}
              />
            </View>
          )}
        </GlassContainer>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Body Measurements</Text>
          <TouchableOpacity onPress={() => setShowAddForm(true)} style={styles.addButton}>
            <Ionicons name="add" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Key Metrics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Key Metrics</Text>
            <View style={styles.metricsGrid}>
              {renderMetricCard('weight', 'Weight', 'kg', 'scale-outline')}
              {renderMetricCard('bodyFat', 'Body Fat', '%', 'analytics-outline')}
              {renderMetricCard('muscle', 'Muscle Mass', 'kg', 'fitness-outline')}
            </View>
          </View>

          {/* Body Measurements */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Body Measurements</Text>
            <View style={styles.measurementsGrid}>
              {measurementFields.map(field => 
                renderMetricCard(field.key, field.label, 'cm', field.icon)
              )}
            </View>
          </View>

          {/* Chart */}
          {getChartData(selectedMetric).length > 1 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                {selectedMetric === 'weight' ? 'Weight' : 
                 selectedMetric === 'bodyFat' ? 'Body Fat' :
                 selectedMetric === 'muscle' ? 'Muscle Mass' :
                 measurementFields.find(f => f.key === selectedMetric)?.label} Trend
              </Text>
              <GlassContainer style={styles.chartContainer} intensity="light">
                <LineChart
                  style={styles.chart}
                  data={getChartData(selectedMetric)}
                  svg={{ stroke: Colors.accent.primary, strokeWidth: 3 }}
                  contentInset={{ top: 20, bottom: 20, left: 20, right: 20 }}
                />
              </GlassContainer>
            </View>
          )}

          {/* Recent Measurements */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Measurements</Text>
            {measurements.slice(-5).reverse().map((measurement) => (
              <GlassContainer key={measurement.id} style={styles.measurementItem} intensity="light">
                <View style={styles.measurementHeader}>
                  <Text style={styles.measurementDate}>
                    {measurement.date.toLocaleDateString()}
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      Alert.alert(
                        'Delete Measurement',
                        'Are you sure you want to delete this measurement?',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          { 
                            text: 'Delete', 
                            style: 'destructive',
                            onPress: () => onDeleteMeasurement(measurement.id)
                          },
                        ]
                      );
                    }}
                  >
                    <Ionicons name="trash-outline" size={16} color={Colors.text.tertiary} />
                  </TouchableOpacity>
                </View>
                <View style={styles.measurementValues}>
                  {measurement.weight && (
                    <Text style={styles.measurementValue}>Weight: {measurement.weight}kg</Text>
                  )}
                  {measurement.bodyFatPercentage && (
                    <Text style={styles.measurementValue}>Body Fat: {measurement.bodyFatPercentage}%</Text>
                  )}
                  {Object.entries(measurement.measurements).map(([key, value]) => (
                    <Text key={key} style={styles.measurementValue}>
                      {measurementFields.find(f => f.key === key)?.label}: {value}cm
                    </Text>
                  ))}
                </View>
                {measurement.notes && (
                  <Text style={styles.measurementNotes}>{measurement.notes}</Text>
                )}
              </GlassContainer>
            ))}
          </View>
        </ScrollView>

        {/* Add Measurement Modal */}
        <Modal
          visible={showAddForm}
          animationType="slide"
          transparent
          onRequestClose={() => setShowAddForm(false)}
        >
          <View style={styles.formOverlay}>
            <GlassContainer style={styles.formContainer} intensity="strong">
              <ScrollView showsVerticalScrollIndicator={false}>
                <Text style={styles.formTitle}>Add Measurement</Text>
                
                {/* Key Metrics */}
                <View style={styles.formSection}>
                  <Text style={styles.formSectionTitle}>Key Metrics</Text>
                  <View style={styles.formRow}>
                    <View style={styles.formField}>
                      <Text style={styles.formLabel}>Weight (kg)</Text>
                      <TextInput
                        style={styles.formInput}
                        value={newMeasurement.weight}
                        onChangeText={(value) => setNewMeasurement(prev => ({ ...prev, weight: value }))}
                        placeholder="75.0"
                        placeholderTextColor={Colors.text.tertiary}
                        keyboardType="numeric"
                      />
                    </View>
                    <View style={styles.formField}>
                      <Text style={styles.formLabel}>Body Fat (%)</Text>
                      <TextInput
                        style={styles.formInput}
                        value={newMeasurement.bodyFatPercentage}
                        onChangeText={(value) => setNewMeasurement(prev => ({ ...prev, bodyFatPercentage: value }))}
                        placeholder="15.0"
                        placeholderTextColor={Colors.text.tertiary}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                  <View style={styles.formField}>
                    <Text style={styles.formLabel}>Muscle Mass (kg)</Text>
                    <TextInput
                      style={styles.formInput}
                      value={newMeasurement.muscleMass}
                      onChangeText={(value) => setNewMeasurement(prev => ({ ...prev, muscleMass: value }))}
                      placeholder="35.0"
                      placeholderTextColor={Colors.text.tertiary}
                      keyboardType="numeric"
                    />
                  </View>
                </View>

                {/* Body Measurements */}
                <View style={styles.formSection}>
                  <Text style={styles.formSectionTitle}>Body Measurements (cm)</Text>
                  {measurementFields.map((field) => (
                    <View key={field.key} style={styles.formField}>
                      <Text style={styles.formLabel}>{field.label}</Text>
                      <TextInput
                        style={styles.formInput}
                        value={newMeasurement.measurements[field.key] || ''}
                        onChangeText={(value) => updateMeasurementField(field.key, value)}
                        placeholder="0.0"
                        placeholderTextColor={Colors.text.tertiary}
                        keyboardType="numeric"
                      />
                    </View>
                  ))}
                </View>

                {/* Notes */}
                <View style={styles.formField}>
                  <Text style={styles.formLabel}>Notes (optional)</Text>
                  <TextInput
                    style={[styles.formInput, styles.notesInput]}
                    value={newMeasurement.notes}
                    onChangeText={(value) => setNewMeasurement(prev => ({ ...prev, notes: value }))}
                    placeholder="Add notes about your measurements..."
                    placeholderTextColor={Colors.text.tertiary}
                    multiline
                    numberOfLines={3}
                  />
                </View>

                <View style={styles.formActions}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowAddForm(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleAddMeasurement}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>
            </GlassContainer>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  addButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  measurementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: (width - 60) / 3,
    marginBottom: 12,
  },
  selectedMetricCard: {
    transform: [{ scale: 1.02 }],
  },
  metricContainer: {
    padding: 12,
    alignItems: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  changeText: {
    fontSize: 10,
    marginLeft: 2,
  },
  miniChart: {
    width: '100%',
    height: 30,
  },
  chartContainer: {
    padding: 16,
    height: 200,
  },
  chart: {
    flex: 1,
  },
  measurementItem: {
    padding: 16,
    marginBottom: 12,
  },
  measurementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  measurementDate: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  measurementValues: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  measurementValue: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginRight: 12,
    marginBottom: 4,
  },
  measurementNotes: {
    fontSize: 12,
    color: Colors.text.primary,
    marginTop: 8,
    fontStyle: 'italic',
  },
  formOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: width * 0.9,
    maxHeight: '80%',
    padding: 20,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  formSection: {
    marginBottom: 20,
  },
  formSectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formField: {
    flex: 1,
    marginRight: 8,
    marginBottom: 12,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 6,
  },
  formInput: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: Colors.text.primary,
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: Colors.accent.primary,
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: Colors.text.inverse,
    fontWeight: '500',
    textAlign: 'center',
  },
});
