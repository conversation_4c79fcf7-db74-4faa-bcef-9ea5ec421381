import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from '../ui/GlassContainer';
import { Colors } from '../../constants/Colors';
import { WorkoutTemplate, TemplateExercise } from '../../types/workout';
import { Exercise } from '../../types/workout';

interface WorkoutTemplatesProps {
  visible: boolean;
  onClose: () => void;
  onSelectTemplate: (template: WorkoutTemplate) => void;
  onCreateFromTemplate: (template: WorkoutTemplate) => void;
}

// Mock template data
const mockTemplates: WorkoutTemplate[] = [
  {
    id: 'template1',
    name: 'Push Day - Chest & Triceps',
    description: 'Complete upper body push workout focusing on chest, shoulders, and triceps',
    createdBy: 'user1',
    exercises: [
      { exerciseId: 'bench-press', order: 1, targetSets: 4, targetReps: 8, targetWeight: 80, restTime: 180 },
      { exerciseId: 'incline-press', order: 2, targetSets: 3, targetReps: 10, targetWeight: 60, restTime: 120 },
      { exerciseId: 'dips', order: 3, targetSets: 3, targetReps: 12, restTime: 90 },
      { exerciseId: 'overhead-press', order: 4, targetSets: 3, targetReps: 8, targetWeight: 50, restTime: 120 },
    ],
    estimatedDuration: 75,
    difficulty: 'intermediate',
    category: 'Strength',
    tags: ['push', 'chest', 'triceps', 'upper body'],
    isPublic: true,
    uses: 1247,
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 'template2',
    name: 'Full Body Beginner',
    description: 'Perfect starter workout hitting all major muscle groups',
    createdBy: 'user2',
    exercises: [
      { exerciseId: 'squat', order: 1, targetSets: 3, targetReps: 12, targetWeight: 40, restTime: 120 },
      { exerciseId: 'bench-press', order: 2, targetSets: 3, targetReps: 10, targetWeight: 50, restTime: 120 },
      { exerciseId: 'bent-over-row', order: 3, targetSets: 3, targetReps: 10, targetWeight: 40, restTime: 120 },
      { exerciseId: 'overhead-press', order: 4, targetSets: 2, targetReps: 8, targetWeight: 30, restTime: 90 },
    ],
    estimatedDuration: 45,
    difficulty: 'beginner',
    category: 'General Fitness',
    tags: ['full body', 'beginner', 'compound'],
    isPublic: true,
    uses: 2341,
    rating: 4.9,
    reviews: [],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
  },
];

export default function WorkoutTemplates({ 
  visible, 
  onClose, 
  onSelectTemplate, 
  onCreateFromTemplate 
}: WorkoutTemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [sortBy, setSortBy] = useState<'popular' | 'rating' | 'recent'>('popular');

  const categories = ['all', 'Strength', 'Cardio', 'General Fitness', 'Bodybuilding', 'Powerlifting'];
  const difficulties = ['all', 'beginner', 'intermediate', 'advanced'];

  const filteredTemplates = mockTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty;
    return matchesSearch && matchesCategory && matchesDifficulty;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.uses - a.uses;
      case 'rating':
        return b.rating - a.rating;
      case 'recent':
        return b.updatedAt.getTime() - a.updatedAt.getTime();
      default:
        return 0;
    }
  });

  const renderTemplateCard = ({ item }: { item: WorkoutTemplate }) => (
    <GlassContainer style={styles.templateCard} intensity="light">
      <View style={styles.templateHeader}>
        <View style={styles.templateInfo}>
          <Text style={styles.templateName}>{item.name}</Text>
          <Text style={styles.templateDescription}>{item.description}</Text>
        </View>
        <View style={styles.templateStats}>
          <View style={styles.statItem}>
            <Ionicons name="star" size={14} color={Colors.accent.primary} />
            <Text style={styles.statText}>{item.rating}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="people" size={14} color={Colors.text.tertiary} />
            <Text style={styles.statText}>{item.uses}</Text>
          </View>
        </View>
      </View>

      <View style={styles.templateDetails}>
        <View style={styles.detailItem}>
          <Ionicons name="time" size={16} color={Colors.text.secondary} />
          <Text style={styles.detailText}>{item.estimatedDuration} min</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="fitness" size={16} color={Colors.text.secondary} />
          <Text style={styles.detailText}>{item.exercises.length} exercises</Text>
        </View>
        <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(item.difficulty) }]}>
          <Text style={styles.difficultyText}>{item.difficulty}</Text>
        </View>
      </View>

      <View style={styles.templateTags}>
        {item.tags.slice(0, 3).map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>

      <View style={styles.templateActions}>
        <TouchableOpacity 
          style={styles.previewButton}
          onPress={() => onSelectTemplate(item)}
        >
          <Ionicons name="eye-outline" size={18} color={Colors.text.secondary} />
          <Text style={styles.previewText}>Preview</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.useButton}
          onPress={() => onCreateFromTemplate(item)}
        >
          <Ionicons name="play" size={18} color={Colors.text.inverse} />
          <Text style={styles.useButtonText}>Start Workout</Text>
        </TouchableOpacity>
      </View>
    </GlassContainer>
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '#4CAF50';
      case 'intermediate': return '#FF9800';
      case 'advanced': return '#F44336';
      default: return Colors.text.tertiary;
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Workout Templates</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.searchSection}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color={Colors.text.tertiary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search templates..."
              placeholderTextColor={Colors.text.tertiary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Category:</Text>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[styles.filterChip, selectedCategory === category && styles.activeFilterChip]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text style={[
                  styles.filterChipText,
                  selectedCategory === category && styles.activeFilterChipText
                ]}>
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        <View style={styles.sortContainer}>
          <Text style={styles.resultsText}>{filteredTemplates.length} templates</Text>
          <TouchableOpacity style={styles.sortButton}>
            <Text style={styles.sortText}>Sort by {sortBy}</Text>
            <Ionicons name="chevron-down" size={16} color={Colors.text.secondary} />
          </TouchableOpacity>
        </View>

        <FlatList
          data={filteredTemplates}
          renderItem={renderTemplateCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.templatesList}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  searchSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.text.primary,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  filterGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginRight: 12,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: Colors.accent.primary,
  },
  filterChipText: {
    fontSize: 12,
    color: Colors.text.secondary,
    textTransform: 'capitalize',
  },
  activeFilterChipText: {
    color: Colors.text.inverse,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  resultsText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginRight: 4,
  },
  templatesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  templateCard: {
    marginBottom: 16,
    padding: 16,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  templateInfo: {
    flex: 1,
    marginRight: 12,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
  },
  templateStats: {
    alignItems: 'flex-end',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  statText: {
    fontSize: 12,
    color: Colors.text.tertiary,
    marginLeft: 4,
  },
  templateDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  detailText: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 'auto',
  },
  difficultyText: {
    fontSize: 10,
    color: Colors.text.inverse,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  templateTags: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tag: {
    backgroundColor: Colors.background.tertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
  },
  tagText: {
    fontSize: 10,
    color: Colors.text.tertiary,
  },
  templateActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
  },
  previewText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: 6,
  },
  useButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.accent.primary,
  },
  useButtonText: {
    fontSize: 14,
    color: Colors.text.inverse,
    fontWeight: '500',
    marginLeft: 6,
  },
});
