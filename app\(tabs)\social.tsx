import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  Image,
  TextInput,
  Modal,
  Alert,
  Animated,
  Dimensions,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from '../../components/ui/GlassContainer';
import ClubCreation from '../../components/ClubCreation';
import { Colors } from '../../constants/Colors';
import { Post, Club } from '../../types/social';

// Mock data
const mockPosts: Post[] = [
  {
    id: '1',
    authorId: 'user1',
    content: 'Just crushed my deadlift PR! 180kg x 5 reps 💪',
    type: 'achievement',
    timestamp: new Date('2024-01-15T14:30:00'),
    visibility: 'public',
    media: [{
      id: 'media1',
      type: 'image',
      url: 'https://placeholder.com/400x300',
      caption: 'New deadlift PR!'
    }],
    workoutData: {
      workoutId: 'workout1',
      exercises: ['Deadlift', 'Romanian Deadlift', 'Bent-over Row'],
      duration: 75,
      volume: 8500,
      personalRecords: ['Deadlift - 180kg x 5'],
      difficulty: 9,
    },
    tags: ['deadlift', 'PR', 'strength'],
    mentions: [],
    likes: [
      { userId: 'user2', timestamp: new Date(), type: 'fire' },
      { userId: 'user3', timestamp: new Date(), type: 'strong' },
    ],
    comments: [
      {
        id: 'comment1',
        authorId: 'user2',
        content: 'Beast mode! 🔥',
        timestamp: new Date(),
        likes: 3,
        replies: [],
        isEdited: false,
      }
    ],
    shares: 5,
    saves: 12,
    isPinned: false,
    isSponsored: false,
  },
  {
    id: '2',
    authorId: 'user4',
    content: 'Morning run complete! Beautiful sunrise today 🌅',
    type: 'workout',
    timestamp: new Date('2024-01-15T08:00:00'),
    visibility: 'public',
    media: [],
    location: {
      name: 'Central Park',
      city: 'New York',
      country: 'USA',
    },
    tags: ['running', 'cardio', 'morning'],
    mentions: [],
    likes: [
      { userId: 'user1', timestamp: new Date(), type: 'like' },
    ],
    comments: [],
    shares: 2,
    saves: 8,
    isPinned: false,
    isSponsored: false,
  },
];

const mockClubs: Club[] = [
  {
    id: 'club1',
    name: 'Powerlifting Elite',
    description: 'For serious powerlifters pushing their limits',
    createdBy: 'user1',
    createdDate: new Date('2023-06-01'),
    category: 'Powerlifting',
    isPrivate: false,
    requiresApproval: true,
    memberCount: 1247,
    members: [],
    moderators: ['user1'],
    banner: 'https://placeholder.com/800x200',
    icon: 'https://placeholder.com/100x100',
    rules: ['Respect all members', 'No spam', 'Share quality content'],
    tags: ['powerlifting', 'strength', 'competition'],
    events: [],
    pinnedPosts: [],
    analytics: {
      totalPosts: 1854,
      totalEngagement: 12500,
      memberGrowth: [],
      topContributors: [],
    },
    subscription: {
      enabled: true,
      tiers: [{
        id: 'tier1',
        name: 'Pro Member',
        price: 9.99,
        duration: 'monthly',
        benefits: ['Exclusive content', 'Direct coach access', 'Priority support'],
        workoutAccess: true,
        programAccess: true,
        liveEventAccess: true,
        exclusiveContent: true,
        prioritySupport: true,
      }],
      freeContent: 'limited',
    },
  },
];

export default function SocialScreen() {
  const [activeTab, setActiveTab] = useState<'feed' | 'clubs' | 'challenges'>('feed');
  const [feedType, setFeedType] = useState<'following' | 'discover' | 'local'>('following');
  const [showPostForm, setShowPostForm] = useState(false);
  const [postContent, setPostContent] = useState('');

  // Advanced post creation state
  const [postType, setPostType] = useState<'text' | 'workout' | 'progress' | 'achievement'>('text');
  const [selectedWorkout, setSelectedWorkout] = useState<any>(null);
  const [postVisibility, setPostVisibility] = useState<'public' | 'followers' | 'club'>('public');
  const [selectedClub, setSelectedClub] = useState<string | null>(null);
  const [postLocation, setPostLocation] = useState<string>('');
  const [postTags, setPostTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [mentionedUsers, setMentionedUsers] = useState<string[]>([]);
  const [shareToStrava, setShareToStrava] = useState(false);
  const [shareToInstagram, setShareToInstagram] = useState(false);
  const [enableComments, setEnableComments] = useState(true);
  const [allowSharing, setAllowSharing] = useState(true);

  // Club creation state
  const [showClubCreation, setShowClubCreation] = useState(false);
  const [clubs, setClubs] = useState(mockClubs);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const addTag = () => {
    if (currentTag.trim() && !postTags.includes(currentTag.trim())) {
      setPostTags([...postTags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setPostTags(postTags.filter(tag => tag !== tagToRemove));
  };

  const createPost = () => {
    if (!postContent.trim()) {
      Alert.alert('Error', 'Please add some content to your post');
      return;
    }

    // Here you would create the post and upload to backend
    console.log('Creating post:', {
      content: postContent,
      type: postType,
      visibility: postVisibility,
      location: postLocation,
      tags: postTags,
      shareToStrava,
      shareToInstagram,
      enableComments,
      allowSharing,
    });

    // Reset form
    setPostContent('');
    setPostType('text');
    setPostLocation('');
    setPostTags([]);
    setShowPostForm(false);

    Alert.alert('Success', 'Your post has been shared!');
  };

  const handleCreateClub = (clubData: any) => {
    const newClub: Club = {
      id: Date.now().toString(),
      name: clubData.name,
      description: clubData.description,
      createdBy: 'currentUser', // Would be actual user ID
      createdDate: new Date(),
      category: clubData.category,
      isPrivate: clubData.isPrivate,
      requiresApproval: clubData.requiresApproval,
      memberCount: 1, // Creator is first member
      members: [{
        userId: 'currentUser',
        joinDate: new Date(),
        role: 'admin',
        contributions: 0,
        warnings: 0,
        isBanned: false,
      }],
      moderators: ['currentUser'],
      rules: clubData.rules,
      tags: [clubData.category],
      events: [],
      pinnedPosts: [],
      analytics: {
        totalPosts: 0,
        totalEngagement: 0,
        memberGrowth: [{ date: new Date(), count: 1 }],
        topContributors: [],
      },
      subscription: clubData.monetization.enabled ? {
        enabled: true,
        tiers: [{
          id: 'tier1',
          name: 'Premium',
          price: clubData.monetization.customPrice || 9.99,
          duration: 'monthly',
          benefits: ['Exclusive content', 'Direct access', 'Priority support'],
          workoutAccess: true,
          programAccess: true,
          liveEventAccess: true,
          exclusiveContent: true,
          prioritySupport: true,
        }],
        freeContent: 'limited',
      } : undefined,
    };

    setClubs([newClub, ...clubs]);
    Alert.alert('Success', `${clubData.name} has been created!`);
  };

  const renderPostForm = () => (
    <Modal
      visible={showPostForm}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowPostForm(false)}>
            <Ionicons name="close" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Create Post</Text>
          <TouchableOpacity style={styles.postButton} onPress={createPost}>
            <Text style={styles.postButtonText}>Post</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          {/* Post Type Selector */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Post Type</Text>
            <View style={styles.postTypeSelector}>
              {[
                { key: 'text', title: 'Text', icon: 'chatbubble-outline' },
                { key: 'workout', title: 'Workout', icon: 'fitness-outline' },
                { key: 'progress', title: 'Progress', icon: 'trending-up-outline' },
                { key: 'achievement', title: 'Achievement', icon: 'trophy-outline' },
              ].map((type) => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.typeButton,
                    postType === type.key && styles.activeTypeButton
                  ]}
                  onPress={() => setPostType(type.key as any)}
                >
                  <Ionicons
                    name={type.icon as any}
                    size={20}
                    color={postType === type.key ? Colors.text.inverse : Colors.text.tertiary}
                  />
                  <Text style={[
                    styles.typeButtonText,
                    postType === type.key && styles.activeTypeButtonText
                  ]}>
                    {type.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </GlassContainer>

          {/* Content Input */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>What's on your mind?</Text>
            <TextInput
              style={styles.contentInput}
              value={postContent}
              onChangeText={setPostContent}
              placeholder="Share your fitness journey..."
              placeholderTextColor={Colors.text.tertiary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </GlassContainer>

          {/* Workout Selector (if workout post type) */}
          {postType === 'workout' && (
            <GlassContainer style={styles.formSection} intensity="medium">
              <Text style={styles.sectionLabel}>Select Workout</Text>
              <TouchableOpacity style={styles.workoutSelector}>
                <Ionicons name="fitness-outline" size={20} color={Colors.accent.primary} />
                <Text style={styles.workoutSelectorText}>
                  {selectedWorkout ? selectedWorkout.name : 'Choose from recent workouts'}
                </Text>
                <Ionicons name="chevron-forward" size={20} color={Colors.text.tertiary} />
              </TouchableOpacity>
            </GlassContainer>
          )}

          {/* Location */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Location</Text>
            <View style={styles.locationInput}>
              <Ionicons name="location-outline" size={20} color={Colors.text.tertiary} />
              <TextInput
                style={styles.locationTextInput}
                value={postLocation}
                onChangeText={setPostLocation}
                placeholder="Add location..."
                placeholderTextColor={Colors.text.tertiary}
              />
            </View>
          </GlassContainer>

          {/* Tags */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Tags</Text>
            <View style={styles.tagInput}>
              <TextInput
                style={styles.tagTextInput}
                value={currentTag}
                onChangeText={setCurrentTag}
                placeholder="Add tags..."
                placeholderTextColor={Colors.text.tertiary}
                onSubmitEditing={addTag}
                returnKeyType="done"
              />
              <TouchableOpacity style={styles.addTagButton} onPress={addTag}>
                <Ionicons name="add" size={20} color={Colors.accent.primary} />
              </TouchableOpacity>
            </View>
            {postTags.length > 0 && (
              <View style={styles.tagsList}>
                {postTags.map((tag, index) => (
                  <View key={index} style={styles.tagChip}>
                    <Text style={styles.tagChipText}>#{tag}</Text>
                    <TouchableOpacity onPress={() => removeTag(tag)}>
                      <Ionicons name="close" size={16} color={Colors.text.tertiary} />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </GlassContainer>

          {/* Visibility Settings */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Visibility</Text>
            <View style={styles.visibilitySelector}>
              {[
                { key: 'public', title: 'Public', icon: 'globe-outline', desc: 'Anyone can see this post' },
                { key: 'followers', title: 'Followers', icon: 'people-outline', desc: 'Only your followers' },
                { key: 'club', title: 'Club Only', icon: 'shield-outline', desc: 'Club members only' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.visibilityOption,
                    postVisibility === option.key && styles.activeVisibilityOption
                  ]}
                  onPress={() => setPostVisibility(option.key as any)}
                >
                  <Ionicons
                    name={option.icon as any}
                    size={20}
                    color={postVisibility === option.key ? Colors.accent.primary : Colors.text.tertiary}
                  />
                  <View style={styles.visibilityInfo}>
                    <Text style={[
                      styles.visibilityTitle,
                      postVisibility === option.key && styles.activeVisibilityTitle
                    ]}>
                      {option.title}
                    </Text>
                    <Text style={styles.visibilityDesc}>{option.desc}</Text>
                  </View>
                  <View style={[
                    styles.radioButton,
                    postVisibility === option.key && styles.activeRadioButton
                  ]}>
                    {postVisibility === option.key && (
                      <View style={styles.radioButtonInner} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </GlassContainer>

          {/* Cross-Platform Sharing */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Share To</Text>
            <View style={styles.sharingOptions}>
              <View style={styles.sharingOption}>
                <View style={styles.sharingInfo}>
                  <Ionicons name="logo-instagram" size={20} color="#E4405F" />
                  <Text style={styles.sharingText}>Instagram Stories</Text>
                </View>
                <Switch
                  value={shareToInstagram}
                  onValueChange={setShareToInstagram}
                  trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                  thumbColor={Colors.text.inverse}
                />
              </View>
              <View style={styles.sharingOption}>
                <View style={styles.sharingInfo}>
                  <Ionicons name="bicycle" size={20} color="#FC4C02" />
                  <Text style={styles.sharingText}>Strava</Text>
                </View>
                <Switch
                  value={shareToStrava}
                  onValueChange={setShareToStrava}
                  trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                  thumbColor={Colors.text.inverse}
                />
              </View>
            </View>
          </GlassContainer>

          {/* Post Settings */}
          <GlassContainer style={styles.formSection} intensity="medium">
            <Text style={styles.sectionLabel}>Post Settings</Text>
            <View style={styles.postSettings}>
              <View style={styles.settingOption}>
                <Text style={styles.settingText}>Allow Comments</Text>
                <Switch
                  value={enableComments}
                  onValueChange={setEnableComments}
                  trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                  thumbColor={Colors.text.inverse}
                />
              </View>
              <View style={styles.settingOption}>
                <Text style={styles.settingText}>Allow Sharing</Text>
                <Switch
                  value={allowSharing}
                  onValueChange={setAllowSharing}
                  trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                  thumbColor={Colors.text.inverse}
                />
              </View>
            </View>
          </GlassContainer>

          <View style={{ height: 50 }} />
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderReactionButton = (type: string, count: number, isActive: boolean = false) => (
    <TouchableOpacity style={[styles.reactionButton, isActive && styles.activeReaction]}>
      <Ionicons
        name={getReactionIcon(type)}
        size={16}
        color={isActive ? Colors.social[type as keyof typeof Colors.social] || Colors.accent.primary : Colors.text.tertiary}
      />
      <Text style={[styles.reactionText, isActive && styles.activeReactionText]}>
        {count}
      </Text>
    </TouchableOpacity>
  );

  const getReactionIcon = (type: string): any => {
    const icons: { [key: string]: any } = {
      like: 'heart-outline',
      fire: 'flame-outline',
      strong: 'barbell-outline',
      comment: 'chatbubble-outline',
      share: 'share-outline',
      save: 'bookmark-outline',
    };
    return icons[type] || 'heart-outline';
  };

  const renderPost = ({ item }: { item: Post }) => (
    <GlassContainer style={styles.postContainer} intensity="light">
      <View style={styles.postHeader}>
        <View style={styles.authorInfo}>
          <View style={styles.avatar} />
          <View style={styles.authorDetails}>
            <Text style={styles.authorName}>Alex Johnson</Text>
            <Text style={styles.postTime}>
              {item.timestamp.toLocaleDateString()} • {item.location?.name}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color={Colors.text.tertiary} />
        </TouchableOpacity>
      </View>

      <Text style={styles.postContent}>{item.content}</Text>

      {item.workoutData && (
        <GlassContainer style={styles.workoutData} intensity="medium">
          <View style={styles.workoutHeader}>
            <Ionicons name="fitness-outline" size={20} color={Colors.accent.primary} />
            <Text style={styles.workoutTitle}>Workout Summary</Text>
          </View>
          <View style={styles.workoutStats}>
            <View style={styles.workoutStat}>
              <Text style={styles.statValue}>{item.workoutData.duration} min</Text>
              <Text style={styles.statLabel}>Duration</Text>
            </View>
            <View style={styles.workoutStat}>
              <Text style={styles.statValue}>{item.workoutData.volume.toLocaleString()} kg</Text>
              <Text style={styles.statLabel}>Volume</Text>
            </View>
            <View style={styles.workoutStat}>
              <Text style={styles.statValue}>{item.workoutData.difficulty}/10</Text>
              <Text style={styles.statLabel}>Difficulty</Text>
            </View>
          </View>
          {item.workoutData.personalRecords.length > 0 && (
            <View style={styles.prBadge}>
              <Ionicons name="trophy" size={16} color={Colors.status.warning} />
              <Text style={styles.prText}>Personal Record!</Text>
            </View>
          )}
        </GlassContainer>
      )}

      <View style={styles.postActions}>
        <View style={styles.reactions}>
          {renderReactionButton('fire', item.likes.filter(l => l.type === 'fire').length)}
          {renderReactionButton('strong', item.likes.filter(l => l.type === 'strong').length)}
          {renderReactionButton('like', item.likes.filter(l => l.type === 'like').length)}
        </View>
        <View style={styles.engagement}>
          {renderReactionButton('comment', item.comments.length)}
          {renderReactionButton('share', item.shares)}
          {renderReactionButton('save', item.saves)}
        </View>
      </View>

      {item.tags.length > 0 && (
        <View style={styles.tagContainer}>
          {item.tags.map((tag, index) => (
            <TouchableOpacity key={index} style={styles.hashTag}>
              <Text style={styles.hashTagText}>#{tag}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </GlassContainer>
  );

  const renderClub = ({ item }: { item: Club }) => (
    <GlassContainer style={styles.clubContainer} intensity="light">
      <View style={styles.clubHeader}>
        <View style={styles.clubIcon} />
        <View style={styles.clubInfo}>
          <Text style={styles.clubName}>{item.name}</Text>
          <Text style={styles.clubMembers}>{item.memberCount.toLocaleString()} members</Text>
        </View>
        <View style={styles.clubBadges}>
          {item.subscription?.enabled && (
            <View style={styles.premiumBadge}>
              <Ionicons name="diamond" size={12} color={Colors.status.warning} />
            </View>
          )}
        </View>
      </View>
      <Text style={styles.clubDescription}>{item.description}</Text>
      <View style={styles.clubStats}>
        <View style={styles.clubStat}>
          <Text style={styles.statValue}>{item.analytics.totalPosts}</Text>
          <Text style={styles.statLabel}>Posts</Text>
        </View>
        <View style={styles.clubStat}>
          <Text style={styles.statValue}>{item.analytics.totalEngagement}</Text>
          <Text style={styles.statLabel}>Engagement</Text>
        </View>
      </View>
      <TouchableOpacity style={styles.joinButton}>
        <Text style={styles.joinButtonText}>Join Club</Text>
      </TouchableOpacity>
    </GlassContainer>
  );

  const renderFeedTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.feedFilters}>
        {[
          { key: 'following', title: 'Following' },
          { key: 'discover', title: 'Discover' },
          { key: 'local', title: 'Local' },
        ].map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[styles.filterButton, feedType === filter.key && styles.activeFilter]}
            onPress={() => setFeedType(filter.key as any)}
          >
            <Text style={[
              styles.filterText,
              feedType === filter.key && styles.activeFilterText
            ]}>
              {filter.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <FlatList
        data={mockPosts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.feedList}
      />
    </View>
  );

  const renderClubsTab = () => (
    <View style={styles.tabContent}>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => setShowClubCreation(true)}
      >
        <Ionicons name="add" size={24} color={Colors.text.inverse} />
        <Text style={styles.createButtonText}>Create Club</Text>
      </TouchableOpacity>

      {/* Featured Clubs Section */}
      <GlassContainer style={styles.featuredSection} intensity="medium">
        <Text style={styles.sectionTitle}>Featured Clubs</Text>
        <Text style={styles.sectionSubtitle}>
          Discover popular fitness communities
        </Text>
      </GlassContainer>

      <FlatList
        data={clubs}
        renderItem={renderClub}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.clubsList}
      />
    </View>
  );

  const renderChallengesTab = () => (
    <View style={styles.tabContent}>
      <GlassContainer style={styles.challengeContainer} intensity="medium">
        <Text style={styles.sectionTitle}>Active Challenges</Text>
        <Text style={styles.comingSoonText}>Challenges coming soon!</Text>
        <Text style={styles.comingSoonSubtext}>
          Compete with friends and the community in fitness challenges
        </Text>
      </GlassContainer>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderPostForm()}
      <ClubCreation
        visible={showClubCreation}
        onClose={() => setShowClubCreation(false)}
        onCreateClub={handleCreateClub}
      />
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Social</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="search-outline" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowPostForm(true)}
          >
            <Ionicons name="add-outline" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.tabBar}>
        {[
          { key: 'feed', title: 'Feed', icon: 'home-outline' },
          { key: 'clubs', title: 'Clubs', icon: 'people-outline' },
          { key: 'challenges', title: 'Challenges', icon: 'trophy-outline' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeTab === tab.key && styles.activeTab]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={activeTab === tab.key ? Colors.accent.primary : Colors.text.tertiary}
            />
            <Text style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText
            ]}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'feed' && renderFeedTab()}
        {activeTab === 'clubs' && renderClubsTab()}
        {activeTab === 'challenges' && renderChallengesTab()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: Colors.glass.medium,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.tertiary,
    marginLeft: 6,
  },
  activeTabText: {
    color: Colors.accent.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  tabContent: {
    paddingBottom: 100,
  },
  feedFilters: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
  },
  activeFilter: {
    backgroundColor: Colors.accent.primary,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.tertiary,
  },
  activeFilterText: {
    color: Colors.text.inverse,
  },
  feedList: {
    gap: 16,
  },
  postContainer: {
    gap: 16,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.accent.primary,
  },
  authorDetails: {
    gap: 2,
  },
  authorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  postTime: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  moreButton: {
    padding: 8,
  },
  postContent: {
    fontSize: 16,
    color: Colors.text.primary,
    lineHeight: 22,
  },
  workoutData: {
    gap: 12,
  },
  workoutHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  workoutTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  workoutStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  workoutStat: {
    alignItems: 'center',
    gap: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.accent.primary,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  prBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.glass.strong,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  prText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.status.warning,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border.secondary,
  },
  reactions: {
    flexDirection: 'row',
    gap: 16,
  },
  engagement: {
    flexDirection: 'row',
    gap: 16,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeReaction: {
    backgroundColor: Colors.glass.light,
  },
  reactionText: {
    fontSize: 12,
    color: Colors.text.tertiary,
    fontWeight: '600',
  },
  activeReactionText: {
    color: Colors.accent.primary,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  hashTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.background.tertiary,
  },
  hashTagText: {
    fontSize: 12,
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accent.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    marginBottom: 20,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.inverse,
  },
  clubsList: {
    gap: 16,
  },
  clubContainer: {
    gap: 16,
  },
  clubHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  clubIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.accent.primary,
  },
  clubInfo: {
    flex: 1,
    gap: 4,
  },
  clubName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  clubMembers: {
    fontSize: 14,
    color: Colors.text.tertiary,
  },
  clubBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  premiumBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.glass.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clubDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
  },
  clubStats: {
    flexDirection: 'row',
    gap: 32,
  },
  clubStat: {
    alignItems: 'center',
    gap: 4,
  },
  joinButton: {
    backgroundColor: Colors.accent.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  joinButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text.inverse,
  },
  challengeContainer: {
    alignItems: 'center',
    gap: 16,
    paddingVertical: 40,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  comingSoonText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  comingSoonSubtext: {
    fontSize: 14,
    color: Colors.text.tertiary,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Post creation modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  postButton: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  postButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.inverse,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formSection: {
    marginBottom: 20,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  postTypeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
    gap: 6,
  },
  activeTypeButton: {
    backgroundColor: Colors.accent.primary,
  },
  typeButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.tertiary,
  },
  activeTypeButtonText: {
    color: Colors.text.inverse,
  },
  contentInput: {
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text.primary,
    minHeight: 100,
  },
  workoutSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  workoutSelectorText: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  locationTextInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
  },
  tagInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  tagTextInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
  },
  addTagButton: {
    padding: 4,
  },
  tagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.glass.medium,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  tagChipText: {
    fontSize: 14,
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  visibilitySelector: {
    gap: 12,
  },
  visibilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  activeVisibilityOption: {
    borderColor: Colors.accent.primary,
    backgroundColor: Colors.glass.light,
  },
  visibilityInfo: {
    flex: 1,
    gap: 2,
  },
  visibilityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  activeVisibilityTitle: {
    color: Colors.accent.primary,
  },
  visibilityDesc: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.border.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeRadioButton: {
    borderColor: Colors.accent.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.accent.primary,
  },
  sharingOptions: {
    gap: 12,
  },
  sharingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  sharingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  sharingText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  postSettings: {
    gap: 12,
  },
  settingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  // Featured section styles
  featuredSection: {
    marginBottom: 20,
    alignItems: 'center',
    paddingVertical: 20,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.text.tertiary,
    textAlign: 'center',
    marginTop: 4,
  },
});