/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/marketplace` | `/marketplace`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/programs` | `/programs`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/social` | `/social`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/marketplace` | `/marketplace`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/programs` | `/programs`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/social` | `/social`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/marketplace${`?${string}` | `#${string}` | ''}` | `/marketplace${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/programs${`?${string}` | `#${string}` | ''}` | `/programs${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/social${`?${string}` | `#${string}` | ''}` | `/social${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workouts${`?${string}` | `#${string}` | ''}` | `/workouts${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/marketplace` | `/marketplace`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/programs` | `/programs`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/social` | `/social`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownInputParams; };
    }
  }
}
