export interface Club {
  id: string;
  name: string;
  description: string;
  createdBy: string;
  createdDate: Date;
  category: string;
  isPrivate: boolean;
  requiresApproval: boolean;
  memberCount: number;
  members: ClubMember[];
  moderators: string[]; // user IDs
  banner?: string;
  icon?: string;
  rules: string[];
  tags: string[];
  subscription?: ClubSubscription;
  events: string[]; // event IDs
  pinnedPosts: string[]; // post IDs
  analytics: ClubAnalytics;
}

export interface ClubMember {
  userId: string;
  joinDate: Date;
  role: 'member' | 'moderator' | 'admin';
  tier?: string; // subscription tier if applicable
  contributions: number; // posts, comments, etc.
  warnings: number;
  isBanned: boolean;
}

export interface ClubSubscription {
  enabled: boolean;
  tiers: ClubSubscriptionTier[];
  freeContent: 'all' | 'limited' | 'none';
}

export interface ClubSubscriptionTier {
  id: string;
  name: string;
  price: number;
  duration: 'monthly' | 'yearly';
  benefits: string[];
  workoutAccess: boolean;
  programAccess: boolean;
  liveEventAccess: boolean;
  exclusiveContent: boolean;
  prioritySupport: boolean;
}

export interface ClubAnalytics {
  totalPosts: number;
  totalEngagement: number;
  memberGrowth: {
    date: Date;
    count: number;
  }[];
  topContributors: {
    userId: string;
    contributions: number;
  }[];
  revenue?: number;
  subscriptionMetrics?: {
    totalSubscribers: number;
    churnRate: number;
    averageRevenue: number;
  };
}

export interface Post {
  id: string;
  authorId: string;
  clubId?: string;
  content: string;
  type: 'workout' | 'progress' | 'discussion' | 'achievement' | 'challenge';
  timestamp: Date;
  visibility: 'public' | 'followers' | 'club' | 'premium';
  media: MediaAttachment[];
  workoutData?: PostWorkoutData;
  location?: Location;
  tags: string[];
  mentions: string[]; // user IDs
  likes: Like[];
  comments: PostComment[];
  shares: number;
  saves: number;
  isPinned: boolean;
  isSponsored: boolean;
  sponsorInfo?: SponsorInfo;
}

export interface PostWorkoutData {
  workoutId: string;
  exercises: string[]; // exercise names
  duration: number; // in minutes
  volume: number;
  personalRecords: string[];
  difficulty: number;
}

export interface MediaAttachment {
  id: string;
  type: 'image' | 'video' | 'gif';
  url: string;
  thumbnail?: string;
  duration?: number; // for videos
  caption?: string;
  altText?: string;
}

export interface Location {
  name: string;
  latitude?: number;
  longitude?: number;
  city?: string;
  country?: string;
}

export interface Like {
  userId: string;
  timestamp: Date;
  type: 'like' | 'love' | 'fire' | 'strong' | 'mind_blown';
}

export interface PostComment {
  id: string;
  authorId: string;
  content: string;
  timestamp: Date;
  likes: number;
  replies: PostComment[];
  isEdited: boolean;
  editTimestamp?: Date;
}

export interface SponsorInfo {
  brandName: string;
  brandLogo: string;
  campaignId: string;
  disclosureText: string;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  createdBy: string;
  clubId?: string;
  startDate: Date;
  endDate: Date;
  type: 'individual' | 'team' | 'club';
  category: string;
  rules: string[];
  prizes: Prize[];
  participants: ChallengeParticipant[];
  leaderboard: LeaderboardEntry[];
  isPublic: boolean;
  entryFee?: number;
  requiresVerification: boolean;
  tags: string[];
  media: MediaAttachment[];
}

export interface Prize {
  position: number;
  description: string;
  value?: number;
  sponsor?: string;
  image?: string;
}

export interface ChallengeParticipant {
  userId: string;
  joinDate: Date;
  teamId?: string;
  status: 'active' | 'completed' | 'withdrawn';
  progress: {
    metric: string;
    value: number;
    timestamp: Date;
  }[];
}

export interface LeaderboardEntry {
  userId: string;
  position: number;
  score: number;
  metric: string;
  lastUpdated: Date;
}

export interface Feed {
  id: string;
  type: 'following' | 'discover' | 'club' | 'local';
  posts: Post[];
  lastUpdated: Date;
  hasMore: boolean;
  cursor?: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'mention' | 'club_invite' | 'workout_reminder' | 'achievement' | 'challenge' | 'subscription';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
  actorId?: string; // who triggered the notification
  metadata?: {
    postId?: string;
    clubId?: string;
    challengeId?: string;
    workoutId?: string;
  };
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'strength' | 'endurance' | 'consistency' | 'social' | 'milestone';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  requirements: AchievementRequirement[];
  reward?: {
    type: 'badge' | 'title' | 'points' | 'discount';
    value: string | number;
  };
}

export interface AchievementRequirement {
  type: 'workout_count' | 'streak' | 'volume' | 'pr' | 'social' | 'time';
  value: number;
  timeframe?: 'day' | 'week' | 'month' | 'year' | 'lifetime';
}

export interface UserAchievement {
  id: string;
  userId: string;
  achievementId: string;
  unlockedDate: Date;
  progress: number; // 0-100
  isDisplayed: boolean;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  recipientId: string;
  content: string;
  timestamp: Date;
  isRead: boolean;
  messageType: 'text' | 'image' | 'video' | 'workout' | 'program';
  attachments?: MessageAttachment[];
  replyToId?: string;
  isEdited: boolean;
  editTimestamp?: Date;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'video' | 'workout' | 'program' | 'exercise';
  url?: string;
  data?: any; // workout/program data
  name: string;
  size?: number;
}

export interface Conversation {
  id: string;
  participants: string[]; // user IDs
  type: 'direct' | 'group' | 'support';
  title?: string;
  lastMessage?: Message;
  lastActivity: Date;
  unreadCount: number;
  isArchived: boolean;
  isMuted: boolean;
  isOnline?: boolean; // For real-time status
  typingUsers?: string[]; // Users currently typing
}

export interface Notification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'message' | 'workout_shared' | 'pr_achieved' | 'challenge_invite' | 'live_stream' | 'subscription' | 'payment';
  title: string;
  message: string;
  data?: any; // Additional data based on type
  isRead: boolean;
  timestamp: Date;
  actionUrl?: string;
  imageUrl?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface LiveStream {
  id: string;
  creatorId: string;
  title: string;
  description?: string;
  category: 'workout' | 'tutorial' | 'q_and_a' | 'nutrition' | 'motivation';
  status: 'scheduled' | 'live' | 'ended';
  scheduledTime?: Date;
  startTime?: Date;
  endTime?: Date;
  viewerCount: number;
  maxViewers: number;
  streamUrl?: string;
  thumbnailUrl?: string;
  isSubscriberOnly: boolean;
  allowChat: boolean;
  allowQuestions: boolean;
  tags: string[];
  recordingUrl?: string;
  chatMessages: LiveChatMessage[];
}

export interface LiveChatMessage {
  id: string;
  streamId: string;
  userId: string;
  username: string;
  message: string;
  timestamp: Date;
  isHighlighted: boolean;
  isModerator: boolean;
  isCreator: boolean;
  type: 'message' | 'question' | 'tip' | 'system';
  tipAmount?: number;
}

export interface UserSearch {
  query: string;
  filters: {
    location?: string;
    fitnessLevel?: string;
    interests?: string[];
    isCreator?: boolean;
    isVerified?: boolean;
  };
  sortBy: 'relevance' | 'followers' | 'recent' | 'location';
}

export interface ContentReport {
  id: string;
  reporterId: string;
  contentType: 'post' | 'comment' | 'message' | 'profile' | 'workout';
  contentId: string;
  reason: 'spam' | 'harassment' | 'inappropriate' | 'copyright' | 'misinformation' | 'other';
  description?: string;
  timestamp: Date;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  moderatorId?: string;
  moderatorNotes?: string;
  actionTaken?: 'none' | 'warning' | 'content_removed' | 'user_suspended' | 'user_banned';
}