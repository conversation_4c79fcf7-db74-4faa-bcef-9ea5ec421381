import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from './ui/GlassContainer';
import { Colors } from '../constants/Colors';
import { Exercise } from '../types/workout';

interface ExerciseLibraryProps {
  visible: boolean;
  onClose: () => void;
  onSelectExercise: (exercise: Exercise) => void;
}

// Mock exercise data
const mockExercises: Exercise[] = [
  {
    id: '1',
    name: 'Bench Press',
    category: 'chest',
    muscleGroups: ['chest', 'triceps', 'shoulders'],
    equipment: ['barbell', 'bench'],
    instructions: ['Lie on bench', 'Lower bar to chest', 'Press up'],
    isCustom: false,
    difficulty: 'intermediate',
  },
  {
    id: '2',
    name: 'Squat',
    category: 'legs',
    muscleGroups: ['quadriceps', 'glutes', 'hamstrings'],
    equipment: ['barbell', 'squat rack'],
    instructions: ['Stand with feet shoulder-width apart', 'Lower into squat', 'Drive through heels'],
    isCustom: false,
    difficulty: 'intermediate',
  },
  {
    id: '3',
    name: 'Deadlift',
    category: 'back',
    muscleGroups: ['hamstrings', 'glutes', 'back', 'traps'],
    equipment: ['barbell'],
    instructions: ['Stand over bar', 'Grip bar', 'Lift with legs and hips'],
    isCustom: false,
    difficulty: 'advanced',
  },
  {
    id: '4',
    name: 'Pull-ups',
    category: 'back',
    muscleGroups: ['lats', 'biceps', 'rhomboids'],
    equipment: ['pull-up bar'],
    instructions: ['Hang from bar', 'Pull body up', 'Lower with control'],
    isCustom: false,
    difficulty: 'intermediate',
  },
  {
    id: '5',
    name: 'Overhead Press',
    category: 'shoulders',
    muscleGroups: ['shoulders', 'triceps', 'core'],
    equipment: ['barbell'],
    instructions: ['Stand with bar at shoulders', 'Press overhead', 'Lower with control'],
    isCustom: false,
    difficulty: 'intermediate',
  },
];

const categories = [
  { id: 'all', name: 'All', icon: 'fitness-outline' },
  { id: 'chest', name: 'Chest', icon: 'body-outline' },
  { id: 'back', name: 'Back', icon: 'body-outline' },
  { id: 'legs', name: 'Legs', icon: 'walk-outline' },
  { id: 'shoulders', name: 'Shoulders', icon: 'body-outline' },
  { id: 'arms', name: 'Arms', icon: 'barbell-outline' },
];

export default function ExerciseLibrary({ visible, onClose, onSelectExercise }: ExerciseLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredExercises = mockExercises.filter(exercise => {
    const matchesSearch = exercise.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || exercise.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderExerciseItem = ({ item }: { item: Exercise }) => (
    <TouchableOpacity
      style={styles.exerciseItem}
      onPress={() => {
        console.log('Exercise selected:', item.name);
        onSelectExercise(item);
      }}
    >
      <GlassContainer style={styles.exerciseCard} intensity="light">
        <View style={styles.exerciseHeader}>
          <Text style={styles.exerciseName}>{item.name}</Text>
          <View style={styles.difficultyBadge}>
            <Text style={styles.difficultyText}>{item.difficulty}</Text>
          </View>
        </View>
        <Text style={styles.muscleGroups}>
          {item.muscleGroups.join(', ')}
        </Text>
        <View style={styles.exerciseFooter}>
          <View style={styles.equipmentTags}>
            {item.equipment.slice(0, 2).map((equipment, index) => (
              <View key={index} style={styles.equipmentTag}>
                <Text style={styles.equipmentText}>{equipment}</Text>
              </View>
            ))}
          </View>
          <Ionicons name="add-circle" size={24} color={Colors.accent.primary} />
        </View>
      </GlassContainer>
    </TouchableOpacity>
  );

  const renderCategoryItem = ({ item }: { item: typeof categories[0] }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategory
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <Ionicons
        name={item.icon as any}
        size={20}
        color={selectedCategory === item.id ? Colors.text.inverse : Colors.text.tertiary}
      />
      <Text style={[
        styles.categoryText,
        selectedCategory === item.id && styles.selectedCategoryText
      ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Exercise Library</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color={Colors.text.tertiary} />
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search exercises..."
              placeholderTextColor={Colors.text.tertiary}
            />
          </View>
        </View>

        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
          style={styles.categoriesContainer}
        />

        <FlatList
          data={filteredExercises}
          renderItem={renderExerciseItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.exercisesList}
          style={styles.exercisesContainer}
        />
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
  },
  categoriesContainer: {
    maxHeight: 60,
  },
  categoriesList: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    gap: 6,
  },
  selectedCategory: {
    backgroundColor: Colors.accent.primary,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.tertiary,
  },
  selectedCategoryText: {
    color: Colors.text.inverse,
  },
  exercisesContainer: {
    flex: 1,
  },
  exercisesList: {
    padding: 20,
    gap: 12,
  },
  exerciseItem: {
    marginBottom: 4,
  },
  exerciseCard: {
    padding: 16,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
    flex: 1,
  },
  difficultyBadge: {
    backgroundColor: Colors.glass.medium,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    color: Colors.text.secondary,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  muscleGroups: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 12,
    textTransform: 'capitalize',
  },
  exerciseFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  equipmentTags: {
    flexDirection: 'row',
    gap: 6,
    flex: 1,
  },
  equipmentTag: {
    backgroundColor: Colors.background.tertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  equipmentText: {
    fontSize: 12,
    color: Colors.text.tertiary,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
});
