import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Image,
  Alert,
  TextInput,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from '../ui/GlassContainer';
import { Colors } from '../../constants/Colors';
import { ProgressPhoto } from '../../types/workout';

interface ProgressPhotosProps {
  visible: boolean;
  onClose: () => void;
  photos: ProgressPhoto[];
  onAddPhoto: (photo: Omit<ProgressPhoto, 'id'>) => void;
  onDeletePhoto: (photoId: string) => void;
}

const { width } = Dimensions.get('window');
const photoSize = (width - 60) / 2;

// Mock progress photos
const mockPhotos: ProgressPhoto[] = [
  {
    id: '1',
    url: 'https://placeholder.com/400x600',
    type: 'front',
    date: new Date('2024-01-01'),
    notes: 'Starting my fitness journey!',
    bodyWeight: 75,
    bodyFatPercentage: 18,
  },
  {
    id: '2',
    url: 'https://placeholder.com/400x600',
    type: 'side',
    date: new Date('2024-01-01'),
    bodyWeight: 75,
    bodyFatPercentage: 18,
  },
  {
    id: '3',
    url: 'https://placeholder.com/400x600',
    type: 'front',
    date: new Date('2024-02-01'),
    notes: 'One month progress - feeling stronger!',
    bodyWeight: 73,
    bodyFatPercentage: 16,
  },
];

export default function ProgressPhotos({ 
  visible, 
  onClose, 
  photos = mockPhotos, 
  onAddPhoto, 
  onDeletePhoto 
}: ProgressPhotosProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<ProgressPhoto | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newPhotoType, setNewPhotoType] = useState<ProgressPhoto['type']>('front');
  const [newPhotoNotes, setNewPhotoNotes] = useState('');
  const [newPhotoWeight, setNewPhotoWeight] = useState('');
  const [newPhotoBodyFat, setNewPhotoBodyFat] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'timeline'>('grid');

  const photoTypes: { key: ProgressPhoto['type']; label: string; icon: string }[] = [
    { key: 'front', label: 'Front', icon: 'person-outline' },
    { key: 'side', label: 'Side', icon: 'person-outline' },
    { key: 'back', label: 'Back', icon: 'person-outline' },
    { key: 'pose', label: 'Pose', icon: 'fitness-outline' },
    { key: 'comparison', label: 'Comparison', icon: 'images-outline' },
  ];

  const groupedPhotos = photos.reduce((groups, photo) => {
    const dateKey = photo.date.toISOString().split('T')[0];
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(photo);
    return groups;
  }, {} as Record<string, ProgressPhoto[]>);

  const sortedDates = Object.keys(groupedPhotos).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  const handleAddPhoto = () => {
    // In a real app, this would open camera/gallery
    Alert.alert(
      'Add Photo',
      'Choose photo source',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = () => {
    // Mock camera functionality
    const newPhoto: Omit<ProgressPhoto, 'id'> = {
      url: 'https://placeholder.com/400x600',
      type: newPhotoType,
      date: new Date(),
      notes: newPhotoNotes,
      bodyWeight: newPhotoWeight ? parseFloat(newPhotoWeight) : undefined,
      bodyFatPercentage: newPhotoBodyFat ? parseFloat(newPhotoBodyFat) : undefined,
    };
    onAddPhoto(newPhoto);
    setShowAddForm(false);
    resetForm();
  };

  const openGallery = () => {
    // Mock gallery functionality
    openCamera(); // Same as camera for now
  };

  const resetForm = () => {
    setNewPhotoNotes('');
    setNewPhotoWeight('');
    setNewPhotoBodyFat('');
    setNewPhotoType('front');
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const renderPhotoGrid = () => (
    <ScrollView style={styles.photosContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.photoGrid}>
        {photos.map((photo) => (
          <TouchableOpacity
            key={photo.id}
            style={styles.photoItem}
            onPress={() => setSelectedPhoto(photo)}
          >
            <Image source={{ uri: photo.url }} style={styles.photoImage} />
            <View style={styles.photoOverlay}>
              <Text style={styles.photoType}>{photo.type}</Text>
              <Text style={styles.photoDate}>{formatDate(photo.date)}</Text>
            </View>
          </TouchableOpacity>
        ))}
        <TouchableOpacity style={styles.addPhotoButton} onPress={() => setShowAddForm(true)}>
          <Ionicons name="add" size={32} color={Colors.accent.primary} />
          <Text style={styles.addPhotoText}>Add Photo</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderTimeline = () => (
    <ScrollView style={styles.photosContainer} showsVerticalScrollIndicator={false}>
      {sortedDates.map((dateKey) => (
        <View key={dateKey} style={styles.timelineGroup}>
          <Text style={styles.timelineDate}>{formatDate(new Date(dateKey))}</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.timelinePhotos}>
              {groupedPhotos[dateKey].map((photo) => (
                <TouchableOpacity
                  key={photo.id}
                  style={styles.timelinePhoto}
                  onPress={() => setSelectedPhoto(photo)}
                >
                  <Image source={{ uri: photo.url }} style={styles.timelinePhotoImage} />
                  <Text style={styles.timelinePhotoType}>{photo.type}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      ))}
    </ScrollView>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Progress Photos</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.viewModeButton}
              onPress={() => setViewMode(viewMode === 'grid' ? 'timeline' : 'grid')}
            >
              <Ionicons 
                name={viewMode === 'grid' ? 'list' : 'grid'} 
                size={20} 
                color={Colors.text.secondary} 
              />
            </TouchableOpacity>
          </View>
        </View>

        {viewMode === 'grid' ? renderPhotoGrid() : renderTimeline()}

        {/* Photo Detail Modal */}
        <Modal
          visible={selectedPhoto !== null}
          animationType="fade"
          transparent
          onRequestClose={() => setSelectedPhoto(null)}
        >
          <View style={styles.photoDetailOverlay}>
            <TouchableOpacity 
              style={styles.photoDetailBackground}
              onPress={() => setSelectedPhoto(null)}
            />
            {selectedPhoto && (
              <GlassContainer style={styles.photoDetailContainer} intensity="strong">
                <Image source={{ uri: selectedPhoto.url }} style={styles.photoDetailImage} />
                <View style={styles.photoDetailInfo}>
                  <Text style={styles.photoDetailType}>{selectedPhoto.type}</Text>
                  <Text style={styles.photoDetailDate}>{formatDate(selectedPhoto.date)}</Text>
                  {selectedPhoto.bodyWeight && (
                    <Text style={styles.photoDetailStat}>Weight: {selectedPhoto.bodyWeight}kg</Text>
                  )}
                  {selectedPhoto.bodyFatPercentage && (
                    <Text style={styles.photoDetailStat}>Body Fat: {selectedPhoto.bodyFatPercentage}%</Text>
                  )}
                  {selectedPhoto.notes && (
                    <Text style={styles.photoDetailNotes}>{selectedPhoto.notes}</Text>
                  )}
                </View>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => {
                    Alert.alert(
                      'Delete Photo',
                      'Are you sure you want to delete this photo?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { 
                          text: 'Delete', 
                          style: 'destructive',
                          onPress: () => {
                            onDeletePhoto(selectedPhoto.id);
                            setSelectedPhoto(null);
                          }
                        },
                      ]
                    );
                  }}
                >
                  <Ionicons name="trash" size={20} color="#FF4444" />
                </TouchableOpacity>
              </GlassContainer>
            )}
          </View>
        </Modal>

        {/* Add Photo Form Modal */}
        <Modal
          visible={showAddForm}
          animationType="slide"
          transparent
          onRequestClose={() => setShowAddForm(false)}
        >
          <View style={styles.formOverlay}>
            <GlassContainer style={styles.formContainer} intensity="strong">
              <Text style={styles.formTitle}>Add Progress Photo</Text>
              
              <View style={styles.photoTypeSelector}>
                <Text style={styles.formLabel}>Photo Type</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.typeButtons}>
                    {photoTypes.map((type) => (
                      <TouchableOpacity
                        key={type.key}
                        style={[
                          styles.typeButton,
                          newPhotoType === type.key && styles.selectedTypeButton
                        ]}
                        onPress={() => setNewPhotoType(type.key)}
                      >
                        <Ionicons 
                          name={type.icon as any} 
                          size={16} 
                          color={newPhotoType === type.key ? Colors.text.inverse : Colors.text.secondary} 
                        />
                        <Text style={[
                          styles.typeButtonText,
                          newPhotoType === type.key && styles.selectedTypeButtonText
                        ]}>
                          {type.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>

              <View style={styles.formRow}>
                <View style={styles.formField}>
                  <Text style={styles.formLabel}>Weight (kg)</Text>
                  <TextInput
                    style={styles.formInput}
                    value={newPhotoWeight}
                    onChangeText={setNewPhotoWeight}
                    placeholder="75.0"
                    placeholderTextColor={Colors.text.tertiary}
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.formField}>
                  <Text style={styles.formLabel}>Body Fat (%)</Text>
                  <TextInput
                    style={styles.formInput}
                    value={newPhotoBodyFat}
                    onChangeText={setNewPhotoBodyFat}
                    placeholder="15.0"
                    placeholderTextColor={Colors.text.tertiary}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.formField}>
                <Text style={styles.formLabel}>Notes (optional)</Text>
                <TextInput
                  style={[styles.formInput, styles.notesInput]}
                  value={newPhotoNotes}
                  onChangeText={setNewPhotoNotes}
                  placeholder="Add notes about your progress..."
                  placeholderTextColor={Colors.text.tertiary}
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowAddForm(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleAddPhoto}
                >
                  <Text style={styles.addButtonText}>Add Photo</Text>
                </TouchableOpacity>
              </View>
            </GlassContainer>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  headerActions: {
    flexDirection: 'row',
  },
  viewModeButton: {
    padding: 8,
  },
  photosContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photoItem: {
    width: photoSize,
    height: photoSize * 1.3,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 8,
  },
  photoType: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text.inverse,
    textTransform: 'capitalize',
  },
  photoDate: {
    fontSize: 10,
    color: Colors.text.inverse,
    opacity: 0.8,
  },
  addPhotoButton: {
    width: photoSize,
    height: photoSize * 1.3,
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.accent.primary,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoText: {
    fontSize: 12,
    color: Colors.accent.primary,
    marginTop: 8,
    fontWeight: '500',
  },
  timelineGroup: {
    marginBottom: 24,
  },
  timelineDate: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  timelinePhotos: {
    flexDirection: 'row',
  },
  timelinePhoto: {
    marginRight: 12,
    alignItems: 'center',
  },
  timelinePhotoImage: {
    width: 80,
    height: 100,
    borderRadius: 8,
  },
  timelinePhotoType: {
    fontSize: 10,
    color: Colors.text.secondary,
    marginTop: 4,
    textTransform: 'capitalize',
  },
  photoDetailOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoDetailBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  photoDetailContainer: {
    width: width * 0.9,
    maxHeight: '80%',
    padding: 20,
  },
  photoDetailImage: {
    width: '100%',
    height: 300,
    borderRadius: 12,
    marginBottom: 16,
  },
  photoDetailInfo: {
    marginBottom: 16,
  },
  photoDetailType: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    textTransform: 'capitalize',
    marginBottom: 4,
  },
  photoDetailDate: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 8,
  },
  photoDetailStat: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  photoDetailNotes: {
    fontSize: 14,
    color: Colors.text.primary,
    lineHeight: 20,
    marginTop: 8,
  },
  deleteButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 8,
    backgroundColor: 'rgba(255,68,68,0.2)',
    borderRadius: 20,
  },
  formOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: width * 0.9,
    padding: 20,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  photoTypeSelector: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  typeButtons: {
    flexDirection: 'row',
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
    marginRight: 8,
  },
  selectedTypeButton: {
    backgroundColor: Colors.accent.primary,
  },
  typeButtonText: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  selectedTypeButtonText: {
    color: Colors.text.inverse,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  formField: {
    flex: 1,
    marginRight: 8,
  },
  formInput: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: Colors.text.primary,
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  addButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: Colors.accent.primary,
    marginLeft: 8,
  },
  addButtonText: {
    fontSize: 16,
    color: Colors.text.inverse,
    fontWeight: '500',
    textAlign: 'center',
  },
});
