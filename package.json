{"name": "everfit", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@stripe/stripe-react-native": "0.45.0", "expo": "^53.0.9", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "^5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-svg-charts": "^5.4.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/webpack-config": "^19.0.1", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.1.3"}, "private": true}