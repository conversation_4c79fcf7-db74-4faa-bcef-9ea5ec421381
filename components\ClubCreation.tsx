import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from './ui/GlassContainer';
import { Colors } from '../constants/Colors';
import { ClubCategory } from '../types/social';

interface ClubCreationProps {
  visible: boolean;
  onClose: () => void;
  onCreateClub: (clubData: any) => void;
}

const clubCategories: { key: ClubCategory; title: string; icon: string; description: string }[] = [
  { key: 'powerlifting', title: 'Powerlifting', icon: 'barbell-outline', description: 'Squat, bench, deadlift focused' },
  { key: 'bodybuilding', title: 'Bodybuilding', icon: 'body-outline', description: 'Muscle building and aesthetics' },
  { key: 'crossfit', title: 'CrossFit', icon: 'fitness-outline', description: 'High-intensity functional fitness' },
  { key: 'yoga', title: 'Yoga', icon: 'leaf-outline', description: 'Flexibility and mindfulness' },
  { key: 'running', title: 'Running', icon: 'walk-outline', description: 'Distance and speed training' },
  { key: 'cycling', title: 'Cycling', icon: 'bicycle-outline', description: 'Road and mountain biking' },
  { key: 'martial_arts', title: 'Martial Arts', icon: 'hand-left-outline', description: 'Combat sports and self-defense' },
  { key: 'dance', title: 'Dance', icon: 'musical-notes-outline', description: 'Dance fitness and choreography' },
  { key: 'nutrition', title: 'Nutrition', icon: 'nutrition-outline', description: 'Diet and meal planning' },
  { key: 'general_fitness', title: 'General Fitness', icon: 'heart-outline', description: 'Overall health and wellness' },
];

const subscriptionTiers = [
  { id: 'free', name: 'Free', price: 0, description: 'Basic access to club content' },
  { id: 'basic', name: 'Basic', price: 4.99, description: 'Access to exclusive workouts' },
  { id: 'premium', name: 'Premium', price: 9.99, description: 'Full access + live sessions' },
  { id: 'elite', name: 'Elite', price: 19.99, description: 'Everything + 1-on-1 coaching' },
];

export default function ClubCreation({ visible, onClose, onCreateClub }: ClubCreationProps) {
  const [clubName, setClubName] = useState('');
  const [clubDescription, setClubDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ClubCategory>('general_fitness');
  const [isPrivate, setIsPrivate] = useState(false);
  const [requiresApproval, setRequiresApproval] = useState(true);
  const [maxMembers, setMaxMembers] = useState('');
  const [clubRules, setClubRules] = useState(['Be respectful to all members', 'No spam or self-promotion', 'Share quality fitness content']);
  const [newRule, setNewRule] = useState('');
  const [enableMonetization, setEnableMonetization] = useState(false);
  const [selectedTier, setSelectedTier] = useState('free');
  const [customPrice, setCustomPrice] = useState('');

  const addRule = () => {
    if (newRule.trim() && !clubRules.includes(newRule.trim())) {
      setClubRules([...clubRules, newRule.trim()]);
      setNewRule('');
    }
  };

  const removeRule = (ruleToRemove: string) => {
    setClubRules(clubRules.filter(rule => rule !== ruleToRemove));
  };

  const handleCreateClub = () => {
    if (!clubName.trim()) {
      Alert.alert('Error', 'Please enter a club name');
      return;
    }

    if (!clubDescription.trim()) {
      Alert.alert('Error', 'Please enter a club description');
      return;
    }

    const clubData = {
      name: clubName,
      description: clubDescription,
      category: selectedCategory,
      isPrivate,
      requiresApproval,
      maxMembers: maxMembers ? parseInt(maxMembers) : undefined,
      rules: clubRules,
      monetization: enableMonetization ? {
        enabled: true,
        tier: selectedTier,
        customPrice: customPrice ? parseFloat(customPrice) : undefined,
      } : { enabled: false },
    };

    onCreateClub(clubData);

    // Reset form
    setClubName('');
    setClubDescription('');
    setSelectedCategory('general_fitness');
    setIsPrivate(false);
    setRequiresApproval(true);
    setMaxMembers('');
    setClubRules(['Be respectful to all members', 'No spam or self-promotion', 'Share quality fitness content']);
    setEnableMonetization(false);
    setSelectedTier('free');
    setCustomPrice('');

    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Create Club</Text>
          <TouchableOpacity style={styles.createButton} onPress={handleCreateClub}>
            <Text style={styles.createButtonText}>Create</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Basic Information */}
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Basic Information</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Club Name</Text>
              <TextInput
                style={styles.textInput}
                value={clubName}
                onChangeText={setClubName}
                placeholder="Enter club name..."
                placeholderTextColor={Colors.text.tertiary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={clubDescription}
                onChangeText={setClubDescription}
                placeholder="Describe your club's purpose and goals..."
                placeholderTextColor={Colors.text.tertiary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
          </GlassContainer>

          {/* Category Selection */}
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Category</Text>
            <View style={styles.categoryGrid}>
              {clubCategories.map((category) => (
                <TouchableOpacity
                  key={category.key}
                  style={[
                    styles.categoryCard,
                    selectedCategory === category.key && styles.selectedCategory
                  ]}
                  onPress={() => setSelectedCategory(category.key)}
                >
                  <Ionicons
                    name={category.icon as any}
                    size={24}
                    color={selectedCategory === category.key ? Colors.accent.primary : Colors.text.tertiary}
                  />
                  <Text style={[
                    styles.categoryTitle,
                    selectedCategory === category.key && styles.selectedCategoryTitle
                  ]}>
                    {category.title}
                  </Text>
                  <Text style={styles.categoryDescription}>{category.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </GlassContainer>

          {/* Privacy Settings */}
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Privacy & Access</Text>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Private Club</Text>
                <Text style={styles.settingDescription}>Only invited members can join</Text>
              </View>
              <Switch
                value={isPrivate}
                onValueChange={setIsPrivate}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Require Approval</Text>
                <Text style={styles.settingDescription}>Review join requests manually</Text>
              </View>
              <Switch
                value={requiresApproval}
                onValueChange={setRequiresApproval}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Max Members (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={maxMembers}
                onChangeText={setMaxMembers}
                placeholder="Leave empty for unlimited"
                placeholderTextColor={Colors.text.tertiary}
                keyboardType="numeric"
              />
            </View>
          </GlassContainer>

          {/* Club Rules */}
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Club Rules</Text>

            <View style={styles.ruleInput}>
              <TextInput
                style={styles.ruleTextInput}
                value={newRule}
                onChangeText={setNewRule}
                placeholder="Add a new rule..."
                placeholderTextColor={Colors.text.tertiary}
                onSubmitEditing={addRule}
                returnKeyType="done"
              />
              <TouchableOpacity style={styles.addRuleButton} onPress={addRule}>
                <Ionicons name="add" size={20} color={Colors.accent.primary} />
              </TouchableOpacity>
            </View>

            <View style={styles.rulesList}>
              {clubRules.map((rule, index) => (
                <View key={index} style={styles.ruleItem}>
                  <Text style={styles.ruleText}>{index + 1}. {rule}</Text>
                  <TouchableOpacity onPress={() => removeRule(rule)}>
                    <Ionicons name="close" size={16} color={Colors.text.tertiary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </GlassContainer>

          {/* Monetization */}
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Monetization</Text>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Enable Paid Membership</Text>
                <Text style={styles.settingDescription}>Charge for premium content access</Text>
              </View>
              <Switch
                value={enableMonetization}
                onValueChange={setEnableMonetization}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>

            {enableMonetization && (
              <View style={styles.monetizationOptions}>
                {subscriptionTiers.map((tier) => (
                  <TouchableOpacity
                    key={tier.id}
                    style={[
                      styles.tierOption,
                      selectedTier === tier.id && styles.selectedTier
                    ]}
                    onPress={() => setSelectedTier(tier.id)}
                  >
                    <View style={styles.tierInfo}>
                      <Text style={[
                        styles.tierName,
                        selectedTier === tier.id && styles.selectedTierName
                      ]}>
                        {tier.name}
                      </Text>
                      <Text style={styles.tierDescription}>{tier.description}</Text>
                    </View>
                    <Text style={[
                      styles.tierPrice,
                      selectedTier === tier.id && styles.selectedTierPrice
                    ]}>
                      ${tier.price}/mo
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </GlassContainer>

          <View style={{ height: 50 }} />
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  createButton: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.inverse,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.secondary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text.primary,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    gap: 8,
  },
  selectedCategory: {
    borderColor: Colors.accent.primary,
    backgroundColor: Colors.glass.light,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    textAlign: 'center',
  },
  selectedCategoryTitle: {
    color: Colors.accent.primary,
  },
  categoryDescription: {
    fontSize: 12,
    color: Colors.text.tertiary,
    textAlign: 'center',
    lineHeight: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.secondary,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: Colors.text.tertiary,
    lineHeight: 16,
  },
  ruleInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    marginBottom: 16,
  },
  ruleTextInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
  },
  addRuleButton: {
    padding: 4,
  },
  rulesList: {
    gap: 8,
  },
  ruleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.glass.light,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  ruleText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text.primary,
    lineHeight: 20,
  },
  monetizationOptions: {
    gap: 12,
    marginTop: 16,
  },
  tierOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectedTier: {
    borderColor: Colors.accent.primary,
    backgroundColor: Colors.glass.light,
  },
  tierInfo: {
    flex: 1,
  },
  tierName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  selectedTierName: {
    color: Colors.accent.primary,
  },
  tierDescription: {
    fontSize: 12,
    color: Colors.text.tertiary,
    lineHeight: 16,
  },
  tierPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.secondary,
  },
  selectedTierPrice: {
    color: Colors.accent.primary,
  },
});
