import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  FlatList,
  Modal,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from '../ui/GlassContainer';
import { Colors } from '../../constants/Colors';
import { Message, Conversation, MessageAttachment } from '../../types/social';

interface MessagingSystemProps {
  visible: boolean;
  onClose: () => void;
  currentUserId: string;
  conversations: Conversation[];
  onSendMessage: (conversationId: string, content: string, attachments?: MessageAttachment[]) => void;
  onMarkAsRead: (conversationId: string) => void;
  onStartConversation: (userId: string) => void;
}

// Mock conversations data
const mockConversations: Conversation[] = [
  {
    id: 'conv1',
    participants: ['user1', 'user2'],
    type: 'direct',
    lastMessage: {
      id: 'msg1',
      conversationId: 'conv1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Great workout today! How did you find those deadlifts?',
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
      isRead: false,
      messageType: 'text',
      isEdited: false,
    },
    lastActivity: new Date(Date.now() - 300000),
    unreadCount: 2,
    isArchived: false,
    isMuted: false,
    isOnline: true,
  },
  {
    id: 'conv2',
    participants: ['user1', 'user3'],
    type: 'direct',
    lastMessage: {
      id: 'msg2',
      conversationId: 'conv2',
      senderId: 'user1',
      recipientId: 'user3',
      content: 'Thanks for sharing that workout template!',
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      isRead: true,
      messageType: 'text',
      isEdited: false,
    },
    lastActivity: new Date(Date.now() - 3600000),
    unreadCount: 0,
    isArchived: false,
    isMuted: false,
    isOnline: false,
  },
];

const mockMessages: Message[] = [
  {
    id: 'msg1',
    conversationId: 'conv1',
    senderId: 'user2',
    recipientId: 'user1',
    content: 'Hey! How was your workout today?',
    timestamp: new Date(Date.now() - 600000),
    isRead: true,
    messageType: 'text',
    isEdited: false,
  },
  {
    id: 'msg2',
    conversationId: 'conv1',
    senderId: 'user1',
    recipientId: 'user2',
    content: 'It was amazing! Hit a new PR on bench press 💪',
    timestamp: new Date(Date.now() - 480000),
    isRead: true,
    messageType: 'text',
    isEdited: false,
  },
  {
    id: 'msg3',
    conversationId: 'conv1',
    senderId: 'user2',
    recipientId: 'user1',
    content: 'Great workout today! How did you find those deadlifts?',
    timestamp: new Date(Date.now() - 300000),
    isRead: false,
    messageType: 'text',
    isEdited: false,
  },
];

export default function MessagingSystem({
  visible,
  onClose,
  currentUserId,
  conversations = mockConversations,
  onSendMessage,
  onMarkAsRead,
  onStartConversation,
}: MessagingSystemProps) {
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>(mockMessages);
  const [messageText, setMessageText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (selectedConversation) {
      // Mark conversation as read when opened
      onMarkAsRead(selectedConversation.id);
      
      // Scroll to bottom when messages change
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [selectedConversation, messages]);

  const handleSendMessage = () => {
    if (!messageText.trim() || !selectedConversation) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      conversationId: selectedConversation.id,
      senderId: currentUserId,
      recipientId: selectedConversation.participants.find(p => p !== currentUserId) || '',
      content: messageText.trim(),
      timestamp: new Date(),
      isRead: false,
      messageType: 'text',
      isEdited: false,
    };

    setMessages(prev => [...prev, newMessage]);
    onSendMessage(selectedConversation.id, messageText.trim());
    setMessageText('');
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatLastSeen = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const renderConversationItem = ({ item }: { item: Conversation }) => (
    <TouchableOpacity
      style={styles.conversationItem}
      onPress={() => setSelectedConversation(item)}
    >
      <GlassContainer style={styles.conversationCard} intensity="light">
        <View style={styles.conversationHeader}>
          <View style={styles.avatarContainer}>
            <View style={[styles.avatar, item.isOnline && styles.onlineAvatar]}>
              <Ionicons name="person" size={20} color={Colors.text.secondary} />
            </View>
            {item.isOnline && <View style={styles.onlineIndicator} />}
          </View>
          <View style={styles.conversationInfo}>
            <View style={styles.conversationTop}>
              <Text style={styles.conversationName}>
                {item.type === 'direct' ? 'User Name' : item.title}
              </Text>
              <Text style={styles.conversationTime}>
                {item.lastMessage ? formatLastSeen(item.lastMessage.timestamp) : ''}
              </Text>
            </View>
            <View style={styles.conversationBottom}>
              <Text style={[
                styles.lastMessage,
                item.unreadCount > 0 && styles.unreadMessage
              ]} numberOfLines={1}>
                {item.lastMessage?.content || 'No messages yet'}
              </Text>
              {item.unreadCount > 0 && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>
                    {item.unreadCount > 99 ? '99+' : item.unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </GlassContainer>
    </TouchableOpacity>
  );

  const renderMessage = (message: Message, index: number) => {
    const isOwnMessage = message.senderId === currentUserId;
    const showTimestamp = index === 0 || 
      messages[index - 1].timestamp.getTime() - message.timestamp.getTime() > 300000; // 5 minutes

    return (
      <View key={message.id} style={styles.messageContainer}>
        {showTimestamp && (
          <Text style={styles.messageTimestamp}>
            {formatTime(message.timestamp)}
          </Text>
        )}
        <View style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownMessage : styles.otherMessage
        ]}>
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText
          ]}>
            {message.content}
          </Text>
          {isOwnMessage && (
            <View style={styles.messageStatus}>
              <Ionicons 
                name={message.isRead ? 'checkmark-done' : 'checkmark'} 
                size={12} 
                color={message.isRead ? Colors.accent.primary : Colors.text.tertiary} 
              />
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderConversationsList = () => (
    <View style={styles.conversationsContainer}>
      <View style={styles.conversationsHeader}>
        <Text style={styles.conversationsTitle}>Messages</Text>
        <TouchableOpacity style={styles.newMessageButton}>
          <Ionicons name="create-outline" size={20} color={Colors.accent.primary} />
        </TouchableOpacity>
      </View>
      <FlatList
        data={conversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.conversationsList}
      />
    </View>
  );

  const renderChatView = () => {
    if (!selectedConversation) return null;

    const conversationMessages = messages.filter(m => m.conversationId === selectedConversation.id);

    return (
      <View style={styles.chatContainer}>
        <View style={styles.chatHeader}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => setSelectedConversation(null)}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <View style={styles.chatHeaderInfo}>
            <Text style={styles.chatHeaderName}>User Name</Text>
            <Text style={styles.chatHeaderStatus}>
              {selectedConversation.isOnline ? 'Online' : `Last seen ${formatLastSeen(selectedConversation.lastActivity)}`}
            </Text>
          </View>
          <TouchableOpacity style={styles.chatOptionsButton}>
            <Ionicons name="ellipsis-vertical" size={20} color={Colors.text.secondary} />
          </TouchableOpacity>
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesList}
          showsVerticalScrollIndicator={false}
        >
          {conversationMessages.map((message, index) => renderMessage(message, index))}
          {typingUsers.length > 0 && (
            <View style={styles.typingIndicator}>
              <Text style={styles.typingText}>User is typing...</Text>
            </View>
          )}
        </ScrollView>

        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.messageInputContainer}
        >
          <GlassContainer style={styles.messageInputWrapper} intensity="medium">
            <TouchableOpacity style={styles.attachButton}>
              <Ionicons name="add" size={20} color={Colors.text.secondary} />
            </TouchableOpacity>
            <TextInput
              style={styles.messageInput}
              value={messageText}
              onChangeText={setMessageText}
              placeholder="Type a message..."
              placeholderTextColor={Colors.text.tertiary}
              multiline
              maxLength={1000}
            />
            <TouchableOpacity 
              style={[styles.sendButton, messageText.trim() && styles.sendButtonActive]}
              onPress={handleSendMessage}
              disabled={!messageText.trim()}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={messageText.trim() ? Colors.text.inverse : Colors.text.tertiary} 
              />
            </TouchableOpacity>
          </GlassContainer>
        </KeyboardAvoidingView>
      </View>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>
            {selectedConversation ? 'Chat' : 'Messages'}
          </Text>
          <View style={styles.placeholder} />
        </View>

        {selectedConversation ? renderChatView() : renderConversationsList()}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  conversationsContainer: {
    flex: 1,
  },
  conversationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  conversationsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  newMessageButton: {
    padding: 8,
  },
  conversationsList: {
    paddingHorizontal: 20,
  },
  conversationItem: {
    marginBottom: 12,
  },
  conversationCard: {
    padding: 16,
  },
  conversationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineAvatar: {
    borderWidth: 2,
    borderColor: Colors.accent.primary,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: Colors.background.primary,
  },
  conversationInfo: {
    flex: 1,
  },
  conversationTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  conversationTime: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  conversationBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: 14,
    color: Colors.text.secondary,
    marginRight: 8,
  },
  unreadMessage: {
    fontWeight: '500',
    color: Colors.text.primary,
  },
  unreadBadge: {
    backgroundColor: Colors.accent.primary,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadCount: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.text.inverse,
  },
  chatContainer: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.background.secondary,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  chatHeaderInfo: {
    flex: 1,
  },
  chatHeaderName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  chatHeaderStatus: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  chatOptionsButton: {
    padding: 8,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  messageContainer: {
    marginBottom: 12,
  },
  messageTimestamp: {
    fontSize: 12,
    color: Colors.text.tertiary,
    textAlign: 'center',
    marginBottom: 8,
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    position: 'relative',
  },
  ownMessage: {
    backgroundColor: Colors.accent.primary,
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    backgroundColor: Colors.background.secondary,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  ownMessageText: {
    color: Colors.text.inverse,
  },
  otherMessageText: {
    color: Colors.text.primary,
  },
  messageStatus: {
    position: 'absolute',
    bottom: 2,
    right: 4,
  },
  typingIndicator: {
    alignSelf: 'flex-start',
    backgroundColor: Colors.background.secondary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    marginTop: 8,
  },
  typingText: {
    fontSize: 12,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  messageInputContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  messageInputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  attachButton: {
    padding: 8,
    marginRight: 8,
  },
  messageInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    padding: 8,
    marginLeft: 8,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
  },
  sendButtonActive: {
    backgroundColor: Colors.accent.primary,
  },
});
