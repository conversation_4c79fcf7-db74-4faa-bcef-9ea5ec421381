import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Alert,
  Vibration,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LineChart } from 'react-native-svg-charts';
import GlassContainer from '../ui/GlassContainer';
import { Colors } from '../../constants/Colors';
import { PersonalRecord } from '../../types/workout';

interface PersonalRecordsProps {
  visible: boolean;
  onClose: () => void;
  records: PersonalRecord[];
  onVerifyRecord: (recordId: string) => void;
  onDeleteRecord: (recordId: string) => void;
}

// Mock personal records data
const mockRecords: PersonalRecord[] = [
  {
    id: 'pr1',
    exerciseId: 'bench-press',
    exerciseName: 'Bench Press',
    type: '1RM',
    value: 120,
    unit: 'kg',
    date: new Date('2024-03-15'),
    workoutId: 'workout1',
    previousRecord: 115,
    improvement: 5,
    verified: true,
  },
  {
    id: 'pr2',
    exerciseId: 'squat',
    exerciseName: 'Squat',
    type: '1RM',
    value: 150,
    unit: 'kg',
    date: new Date('2024-03-10'),
    workoutId: 'workout2',
    previousRecord: 145,
    improvement: 5,
    verified: true,
  },
  {
    id: 'pr3',
    exerciseId: 'deadlift',
    exerciseName: 'Deadlift',
    type: '1RM',
    value: 180,
    unit: 'kg',
    date: new Date('2024-03-08'),
    workoutId: 'workout3',
    previousRecord: 175,
    improvement: 5,
    verified: false,
  },
  {
    id: 'pr4',
    exerciseId: 'bench-press',
    exerciseName: 'Bench Press',
    type: 'volume',
    value: 2400,
    unit: 'kg',
    date: new Date('2024-03-05'),
    workoutId: 'workout4',
    previousRecord: 2200,
    improvement: 200,
    verified: true,
  },
  {
    id: 'pr5',
    exerciseId: 'running',
    exerciseName: '5K Run',
    type: 'time',
    value: 1200, // 20 minutes in seconds
    unit: 'seconds',
    date: new Date('2024-03-01'),
    previousRecord: 1320,
    improvement: -120, // negative because faster is better
    verified: true,
  },
];

const recordTypes = [
  { key: 'all', label: 'All Records', icon: 'trophy-outline' },
  { key: '1RM', label: '1 Rep Max', icon: 'barbell-outline' },
  { key: 'volume', label: 'Volume', icon: 'analytics-outline' },
  { key: 'reps', label: 'Max Reps', icon: 'repeat-outline' },
  { key: 'time', label: 'Best Time', icon: 'time-outline' },
  { key: 'distance', label: 'Distance', icon: 'walk-outline' },
];

export default function PersonalRecords({
  visible,
  onClose,
  records = mockRecords,
  onVerifyRecord,
  onDeleteRecord,
}: PersonalRecordsProps) {
  const [selectedType, setSelectedType] = useState('all');
  const [selectedExercise, setSelectedExercise] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'improvement' | 'exercise'>('date');

  const filteredRecords = records
    .filter(record => selectedType === 'all' || record.type === selectedType)
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return b.date.getTime() - a.date.getTime();
        case 'improvement':
          return Math.abs(b.improvement || 0) - Math.abs(a.improvement || 0);
        case 'exercise':
          return a.exerciseName.localeCompare(b.exerciseName);
        default:
          return 0;
      }
    });

  const exerciseRecords = records.reduce((acc, record) => {
    if (!acc[record.exerciseId]) {
      acc[record.exerciseId] = [];
    }
    acc[record.exerciseId].push(record);
    return acc;
  }, {} as Record<string, PersonalRecord[]>);

  const formatValue = (record: PersonalRecord) => {
    switch (record.unit) {
      case 'kg':
      case 'lbs':
        return `${record.value}${record.unit}`;
      case 'seconds':
        const minutes = Math.floor(record.value / 60);
        const seconds = record.value % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      case 'minutes':
        return `${record.value} min`;
      case 'km':
      case 'miles':
        return `${record.value}${record.unit}`;
      case 'reps':
        return `${record.value} reps`;
      default:
        return record.value.toString();
    }
  };

  const formatImprovement = (record: PersonalRecord) => {
    if (!record.improvement) return null;
    
    const isTimeRecord = record.unit === 'seconds' || record.unit === 'minutes';
    const isImprovement = isTimeRecord ? record.improvement < 0 : record.improvement > 0;
    const value = Math.abs(record.improvement);
    
    let formattedValue = '';
    if (record.unit === 'seconds') {
      const minutes = Math.floor(value / 60);
      const seconds = value % 60;
      formattedValue = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } else {
      formattedValue = `${value}${record.unit}`;
    }
    
    return {
      text: `${isImprovement ? '+' : '-'}${formattedValue}`,
      color: isImprovement ? '#4CAF50' : '#F44336',
    };
  };

  const getRecordIcon = (type: string) => {
    switch (type) {
      case '1RM': return 'barbell-outline';
      case 'volume': return 'analytics-outline';
      case 'reps': return 'repeat-outline';
      case 'time': return 'time-outline';
      case 'distance': return 'walk-outline';
      default: return 'trophy-outline';
    }
  };

  const handleVerifyRecord = (record: PersonalRecord) => {
    if (record.verified) return;
    
    Alert.alert(
      'Verify Personal Record',
      `Verify your ${record.exerciseName} ${record.type} of ${formatValue(record)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Verify',
          onPress: () => {
            onVerifyRecord(record.id);
            Vibration.vibrate([0, 200, 100, 200]);
          },
        },
      ]
    );
  };

  const renderRecordCard = ({ item }: { item: PersonalRecord }) => {
    const improvement = formatImprovement(item);
    
    return (
      <TouchableOpacity
        style={styles.recordCard}
        onPress={() => handleVerifyRecord(item)}
        onLongPress={() => {
          Alert.alert(
            'Delete Record',
            'Are you sure you want to delete this personal record?',
            [
              { text: 'Cancel', style: 'cancel' },
              { 
                text: 'Delete', 
                style: 'destructive',
                onPress: () => onDeleteRecord(item.id)
              },
            ]
          );
        }}
      >
        <GlassContainer style={styles.recordContainer} intensity="light">
          <View style={styles.recordHeader}>
            <View style={styles.recordInfo}>
              <View style={styles.recordTitleRow}>
                <Ionicons 
                  name={getRecordIcon(item.type) as any} 
                  size={20} 
                  color={Colors.accent.primary} 
                />
                <Text style={styles.exerciseName}>{item.exerciseName}</Text>
                {!item.verified && (
                  <View style={styles.unverifiedBadge}>
                    <Text style={styles.unverifiedText}>!</Text>
                  </View>
                )}
              </View>
              <Text style={styles.recordType}>{item.type.toUpperCase()}</Text>
            </View>
            <View style={styles.recordValue}>
              <Text style={styles.valueText}>{formatValue(item)}</Text>
              {improvement && (
                <Text style={[styles.improvementText, { color: improvement.color }]}>
                  {improvement.text}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.recordFooter}>
            <Text style={styles.recordDate}>
              {item.date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: 'numeric'
              })}
            </Text>
            {item.verified ? (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                <Text style={styles.verifiedText}>Verified</Text>
              </View>
            ) : (
              <TouchableOpacity 
                style={styles.verifyButton}
                onPress={() => handleVerifyRecord(item)}
              >
                <Text style={styles.verifyButtonText}>Verify</Text>
              </TouchableOpacity>
            )}
          </View>
        </GlassContainer>
      </TouchableOpacity>
    );
  };

  const renderExerciseProgress = () => {
    if (!selectedExercise || !exerciseRecords[selectedExercise]) return null;
    
    const exerciseData = exerciseRecords[selectedExercise]
      .filter(r => r.type === '1RM')
      .sort((a, b) => a.date.getTime() - b.date.getTime());
    
    if (exerciseData.length < 2) return null;
    
    return (
      <View style={styles.progressSection}>
        <Text style={styles.progressTitle}>Progress Chart</Text>
        <GlassContainer style={styles.chartContainer} intensity="light">
          <LineChart
            style={styles.chart}
            data={exerciseData.map(r => r.value)}
            svg={{ stroke: Colors.accent.primary, strokeWidth: 3 }}
            contentInset={{ top: 20, bottom: 20, left: 20, right: 20 }}
          />
          <View style={styles.chartLabels}>
            <Text style={styles.chartLabel}>
              {exerciseData[0].date.toLocaleDateString('en-US', { month: 'short' })}
            </Text>
            <Text style={styles.chartLabel}>
              {exerciseData[exerciseData.length - 1].date.toLocaleDateString('en-US', { month: 'short' })}
            </Text>
          </View>
        </GlassContainer>
      </View>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Personal Records</Text>
          <TouchableOpacity style={styles.sortButton}>
            <Ionicons name="filter-outline" size={20} color={Colors.text.secondary} />
          </TouchableOpacity>
        </View>

        {/* Record Type Filters */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false} 
          style={styles.filtersContainer}
        >
          <View style={styles.filters}>
            {recordTypes.map((type) => (
              <TouchableOpacity
                key={type.key}
                style={[
                  styles.filterChip,
                  selectedType === type.key && styles.activeFilterChip
                ]}
                onPress={() => setSelectedType(type.key)}
              >
                <Ionicons 
                  name={type.icon as any} 
                  size={16} 
                  color={selectedType === type.key ? Colors.text.inverse : Colors.text.secondary} 
                />
                <Text style={[
                  styles.filterText,
                  selectedType === type.key && styles.activeFilterText
                ]}>
                  {type.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        {/* Statistics */}
        <View style={styles.statsContainer}>
          <GlassContainer style={styles.statsCard} intensity="light">
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{records.length}</Text>
              <Text style={styles.statLabel}>Total PRs</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {records.filter(r => r.verified).length}
              </Text>
              <Text style={styles.statLabel}>Verified</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {records.filter(r => {
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return r.date > weekAgo;
                }).length}
              </Text>
              <Text style={styles.statLabel}>This Week</Text>
            </View>
          </GlassContainer>
        </View>

        {renderExerciseProgress()}

        {/* Records List */}
        <FlatList
          data={filteredRecords}
          renderItem={renderRecordCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.recordsList}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  sortButton: {
    padding: 8,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  filters: {
    flexDirection: 'row',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: Colors.accent.primary,
  },
  filterText: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  activeFilterText: {
    color: Colors.text.inverse,
    fontWeight: '500',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  statsCard: {
    flexDirection: 'row',
    padding: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  statDivider: {
    width: 1,
    backgroundColor: Colors.background.tertiary,
    marginHorizontal: 16,
  },
  progressSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  chartContainer: {
    padding: 16,
    height: 150,
  },
  chart: {
    flex: 1,
  },
  chartLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  chartLabel: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  recordsList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  recordCard: {
    marginBottom: 12,
  },
  recordContainer: {
    padding: 16,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  recordInfo: {
    flex: 1,
  },
  recordTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginLeft: 8,
    flex: 1,
  },
  unverifiedBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
  },
  unverifiedText: {
    fontSize: 12,
    fontWeight: '700',
    color: Colors.text.inverse,
  },
  recordType: {
    fontSize: 12,
    color: Colors.text.secondary,
    fontWeight: '500',
  },
  recordValue: {
    alignItems: 'flex-end',
  },
  valueText: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.accent.primary,
  },
  improvementText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  recordFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  recordDate: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '500',
  },
  verifyButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.accent.primary,
  },
  verifyButtonText: {
    fontSize: 12,
    color: Colors.text.inverse,
    fontWeight: '500',
  },
});
